options(rows=100000,readsize=400000000,bindsize=400000000,PARALLEL=TRUE,ERRORS=0)
LOAD DATA 
CHARACTERSET 'UTF8'
INFILE '/data01/sqlldr/stg_data/ads_cc_index_analy_di/ads_cc_index_analy_di.dat' 
APPEND INTO TABLE PERFORMANCE.ADS_CC_INDEX_ANALY_DI
FIELDS TERMINATED BY '\t' 
(
INIT_DATE,
P_<PERSON>ANCH_NO,
P_BRANCH_NAME,
<PERSON><PERSON>CH_NO,
<PERSON><PERSON>CH_NAME,
CLIENT_ID,
CLIENT_NAME,
ORGAN_FLAG_CODE,
CLIENT_LEVEL_CODE,
CORP_RISK_LEVEL_CODE,
CLIENT_STATUS_CODE,
ORGAN_FLAG,
CLIENT_LEVEL,
CORP_RISK_LEVEL,
CLIENT_STATUS,
OPEN_DATE,
PARTY_NO,
PARTY_NAME,
PARTY_TYPE,
RELATION_TYPE,
RELATION_START_DATE,
SEX,
AG<PERSON>,
BIRTHDAY,
EFFICIENT_CLIENT_BOOL,
HIGH_WORTH_CLIENT_BOOL,
DRAI_CLIENT_BOOL,
DRAI_CHANNEL_CODE,
INNER_CHANGE_CUST_BOOL,
TOTAL_ASSET,
NET_ASSET,
COMM_TOTAL_VALUE,
CRDT_TOTAL_VALUE,
OPT_TOTAL_VALUE,
CRDT_FIN_SLO_DEBIT,
CRDT_FIN_SLO_BALANCE,
FUND_ASSET,
SECU_MTK_VALUE,
OPT_MTK_VALUE,
PROD_MTK_VALUE,
EQU_PRODFUND_MTK_VALUE,
FIXR_PRODFUND_MTK_VALUE,
MONEY_PRODFUND_MTK_VALUE,
PUB_PRODFUND_MTK_VALUE,
PRI_PRODFUND_MTK_VALUE,
TOTAL_TRADE_FARE,
TOTAL_TRADE_NET_FARE,
COMM_TRADE_FARE,
CRDT_TRADE_FARE,
OPT_TRADE_FARE,
ASSURE_TRADE_FARE,
CREDIT_TRADE_FARE,
COMM_TRADE_NET_FARE,
CRDT_TRADE_NET_FARE,
OPT_TRADE_NET_FARE,
ASSURE_TRADE_NET_FARE,
CREDIT_TRADE_NET_FARE,
COMM_STOCKFUND_NET_FARE,
ASSURE_STOCKFUND_NET_FARE,
CREDIT_STOCKFUND_NET_FARE,
STOCKFUND_TRD_BALANCE,
COMM_STOCKFUND_TRD_BALANCE,
CRDT_STOCKFUND_TRD_BALANCE,
CREDIT_STOCKFUND_TRD_BALANCE,
ASSURE_STOCKFUND_TRD_BALANCE,
PRODFUND_BUY_BALANCE,
EQU_PRODFUND_BUY_BALANCE,
FIXR_PRODFUND_BUY_BALANCE,
MONEY_PRODFUND_BUY_BALANCE,
PUB_PRODFUND_BUY_BALANCE,
PRI_PRODFUND_BUY_BALANCE,
AVG_CUST_TOTAL_ASSET_Y,
AVG_TOTAL_ASSET_Y,
AVG_CUST_FIN_SLO_BALANCE_Y,
AVG_FIN_SLO_BALANCE_Y,
AVG_CUST_NET_ASSET_Y,
AVG_NET_ASSET_Y,
AVG_CUST_LIMITSECU_MTK_VALUE_Y,
AVG_LIMITSECU_MTK_VALUE_Y,
AVG_CUST_COMM_TOTAL_VALUE_Y,
AVG_COMM_TOTAL_VALUE_Y,
AVG_CUST_CRDT_TOTAL_VALUE_Y,
AVG_CRDT_TOTAL_VALUE_Y,
AVG_CUST_OPT_TOTAL_VALUE_Y,
AVG_OPT_TOTAL_VALUE_Y,
AVG_CUST_CRDT_FIN_SLO_DEBIT_Y,
AVG_CRDT_FIN_SLO_DEBIT_Y,
AVG_CUST_PROD_MTK_VALUE_Y,
AVG_PROD_MTK_VALUE_Y,
AVG_EQU_PRODFUND_MTK_VALUE_Y,
AVG_FIXR_PRODFUND_MTK_VALUE_Y,
AVG_MONEY_PRODFUND_MTK_VALUE_Y,
AVG_PUB_PRODFUND_MTK_VALUE_Y,
AVG_PRI_PRODFUND_MTK_VALUE_Y,
TOTAL_TRADE_FARE_Y,
TOTAL_TRADE_NET_FARE_Y,
COMM_TRADE_FARE_Y,
CRDT_TRADE_FARE_Y,
OPT_TRADE_FARE_Y,
ASSURE_TRADE_FARE_Y,
CREDIT_TRADE_FARE_Y,
COMM_TRADE_NET_FARE_Y,
CRDT_TRADE_NET_FARE_Y,
OPT_TRADE_NET_FARE_Y,
ASSURE_TRADE_NET_FARE_Y,
CREDIT_TRADE_NET_FARE_Y,
COMM_STOCKFUND_NET_FARE_Y,
ASSURE_STOCKFUND_NET_FARE_Y,
CREDIT_STOCKFUND_NET_FARE_Y,
STOCKFUND_TRD_BALANCE_Y,
COMM_STOCKFUND_TRD_BALANCE_Y,
CRDT_STOCKFUND_TRD_BALANCE_Y,
CREDIT_STOCKFUND_TRD_BALANCE_Y,
ASSURE_STOCKFUND_TRD_BALANCE_Y,
PRODFUND_BUY_BALANCE_Y,
EQU_PRODFUND_BUY_BALANCE_Y,
FIXR_PRODFUND_BUY_BALANCE_Y,
MONEY_PRODFUND_BUY_BALANCE_Y,
PUB_PRODFUND_BUY_BALANCE_Y,
PRI_PRODFUND_BUY_BALANCE_Y,
AVG_TOTAL_OFF_CT_ASSET_20D,
AVG_TOTAL_ASSET_20D,
CRDT_OPEN_DATE,
PROD_EFFICIENT_CLIENT_BOOL,
OPT_FUND_ASSET,
COMM_MTK_VALUE,
CRDT_MTK_VALUE,
COMB_MKT_VALUE,
AVG_COMB_MKT_VALUE_Y,
EXCHFUND_BUY_BALANCE_Y,
EQU_EXCHFUND_BUY_BALANCE_Y,
COL13,
COL14,
COL15,
COL16,
COL17,
COL18,
COL19,
UPDATE_TIME,
BUSI_DATE,
LIMIT_SECU_MTK_VALUE,
UPDATETIME "SYSDATE",
AVG_CUST_EQU_PRODFUND_MTK_VALU
)
