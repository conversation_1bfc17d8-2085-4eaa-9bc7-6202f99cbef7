#数据中心视图
CREATE OR REPLACE VIEW CT_ODS_UF30.TSYS_ROLE_DAILY AS SELECT F.*, TD.TRADEDATE AS BUSI_DATE FROM CT_ODS_UF30.TSYS_ROLE F JOIN (SELECT MAX(TRADE_DAY_ID) AS TRADEDATE FROM CT_ODS_DC.DIM_DATE WHERE YEAR_ID >= EXTRACT(YEAR FROM SYSDATE) AND DAY_ID < (SELECT INIT_DATE FROM CT_ODS_UF20.SYSARG WHERE ROWNUM = 1)) TD ON 1=1



#hive最新分区表-视图
CREATE VIEW `ods_uf30.tsys_role` AS WITH latest_part AS (
  SELECT MAX(`tsys_role_daily`.`part_column`) AS `max_part`
  FROM `ods_uf30_daily`.`tsys_role_daily`
)
SELECT `a`.*
FROM `ods_uf30_daily`.`tsys_role_daily` `a`
JOIN latest_part b
  ON `a`.`part_column` = `b`.`max_part`;


CREATE VIEW `ods_uf30.tsys_hs_function` AS WITH latest_part AS (
  SELECT MAX(`tsys_hs_function_daily`.`part_column`) AS `max_part`
  FROM `ods_uf30_daily`.`tsys_hs_function_daily`
)
SELECT `a`.*
FROM `ods_uf30_daily`.`tsys_hs_function_daily` `a`
JOIN latest_part b
  ON `a`.`part_column` = `b`.`max_part`;
