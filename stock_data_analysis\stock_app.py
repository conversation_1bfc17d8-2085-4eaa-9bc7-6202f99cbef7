"""
# 场内基金技术分析Streamlit应用
# -------------------------
# 启动命令:
#   streamlit run stock_app.py
#
# 依赖安装:
#   pip install streamlit pandas numpy scipy tushare pyecharts scikit-learn MyTT
"""

import streamlit as st
from pathlib import Path
import traceback
from indicator_analysis import StockEchartVisualizer, StockDataManager

# 页面设置
st.set_page_config(page_title="基金技术分析工具", page_icon="📈", layout="wide")

# 创建cache目录(如果不存在)
cache_dir = Path("cache")
cache_dir.mkdir(exist_ok=True)

# 初始化会话状态变量
if "selected_code" not in st.session_state:
    st.session_state.selected_code = ""
if "input_code" not in st.session_state:
    st.session_state.input_code = ""
if "charts" not in st.session_state:
    st.session_state.charts = {}


# 回调函数 - 在运行分析时更新所选代码
def update_and_run():
    # 更新选择的代码
    if st.session_state.fund_selector == "直接输入代码...":
        code = st.session_state.manual_input.strip()
        if code:
            st.session_state.selected_code = auto_append_suffix(code)
    else:
        st.session_state.selected_code = st.session_state.fund_selector.split(":")[
            0
        ].strip()


# 使用Streamlit缓存减少重复计算
@st.cache_data(ttl=3600, show_spinner=False)
def get_available_funds():
    """获取可用基金列表并缓存结果"""
    data_manager = StockDataManager()
    return data_manager.get_available_funds()


# [MODIFIED] 优化了图表生成逻辑
@st.cache_data(ttl=3600, show_spinner="正在生成图表...")
def generate_chart(
    ts_code,
    display_points,
    force_refresh,
    smooth_window,
    add_trendlines,
    extrema_window,
    min_trend_length,
    show_breakthrough,
    add_fibonacci,
    detect_patterns,
):
    """
    生成图表并缓存结果.
    [MODIFIED] 如果无法生成图表(如数据为空),则返回None.
    """
    config = {"theme": "white", "width": 1200, "height": 700}
    visualizer = StockEchartVisualizer(config)

    # 使用固定的长期和短期EMA周期
    long_term = 26
    short_term = 13
    start_date = "20220101"
    end_date = ""

    # 调用优化后的绘图函数
    chart = visualizer.plot_stock_chart(
        ts_code=ts_code,
        display_points=int(display_points),
        force_refresh=force_refresh,
        long_term=long_term,
        short_term=short_term,
        smooth_window=int(smooth_window) if smooth_window else None,
        start_date=start_date,
        end_date=end_date,
        add_trendlines=add_trendlines,
        extrema_window=int(extrema_window),
        min_trend_length=int(min_trend_length),
        show_breakthrough=show_breakthrough,
        add_fibonacci=add_fibonacci,
        detect_patterns=detect_patterns,
    )
    return chart


# 自动判断后缀(.SH或.SZ)
def auto_append_suffix(code):
    code = code.strip().upper().split(".")[0]
    if code.startswith(("5", "6", "7")):
        return f"{code}.SH"
    elif code.startswith(("0", "1", "2", "3")):
        return f"{code}.SZ"
    else:
        return f"{code}.SH"


# --- 侧边栏 ---
with st.sidebar:
    st.header("参数设置")

    try:
        available_funds = get_available_funds()
        fund_options = [f"{code}: {name}" for code, name in available_funds.items()]
    except Exception as e:
        st.error(f"获取基金列表失败: {str(e)}")
        fund_options = []
        available_funds = {}

    fund_input_options = ["直接输入代码..."] + fund_options
    st.selectbox(
        "选择或输入基金代码",
        options=fund_input_options,
        key="fund_selector",
        help="从列表中选择或选择第一项后手动输入代码",
    )

    if st.session_state.fund_selector == "直接输入代码...":
        input_code = st.text_input(
            "输入基金代码",
            key="manual_input",
            placeholder="输入数字代码,将自动添加后缀",
        )
        if input_code:
            st.info(f"完整代码: {auto_append_suffix(input_code)}")
    
    display_points = st.selectbox(
        "显示点数",
        options=[50, 100, 150, 200, 250, 300, 350, 400, 450, 500],
        index=4,  # 默认为250
    )
    smooth_window = st.number_input("平滑窗口大小", min_value=5, max_value=89, value=66)
    
    add_trendlines = st.checkbox("添加趋势线", value=True)
    col1, col2 = st.columns(2)
    with col1:
        extrema_window = st.number_input("趋势线窗口", min_value=5, max_value=50, value=20, disabled=not add_trendlines)
    with col2:
        min_trend_length = st.number_input("最小趋势周期", min_value=20, max_value=120, value=60, disabled=not add_trendlines)

    show_breakthrough = st.checkbox("显示突破点", value=True, disabled=not add_trendlines)
    
    run_button = st.button("运行分析", type="primary", use_container_width=True, on_click=update_and_run)

    st.markdown("---")
    st.markdown("### 高级功能")
    add_fibonacci = st.checkbox("显示斐波那契回调", value=True)
    detect_patterns = st.checkbox("检测价格形态", value=True)
    force_refresh = st.checkbox("强制刷新数据", value=False)
    st.markdown("<br>", unsafe_allow_html=True)

# --- 主内容区 ---
# [MODIFIED] 优化了主内容区的逻辑,以提供更好的用户反馈
if run_button and st.session_state.selected_code:
    ts_code = st.session_state.selected_code
    try:
        chart = generate_chart(
            ts_code=ts_code,
            display_points=display_points,
            force_refresh=force_refresh,
            smooth_window=smooth_window,
            add_trendlines=add_trendlines,
            extrema_window=extrema_window,
            min_trend_length=min_trend_length,
            show_breakthrough=show_breakthrough,
            add_fibonacci=add_fibonacci,
            detect_patterns=detect_patterns,
        )

        # [NEW] 检查图表是否成功生成
        if chart is None:
            st.error(f"无法获取基金 {ts_code} 的数据.请检查代码是否正确或该基金在所选时段内无数据.")
            st.session_state.charts = {}  # 清除旧图表
        else:
            file_path = cache_dir / f"temp_{ts_code}_chart.html"
            chart.render(str(file_path))
            st.session_state.charts = {ts_code: file_path}

    except Exception as e:
        st.error(f"处理 {ts_code} 时发生未知错误: {str(e)}")
        st.error("详细错误信息: " + traceback.format_exc())
        st.session_state.charts = {}

# 根据当前状态渲染UI
if not st.session_state.charts:
    # 如果没有图表,显示初始提示
    st.warning("请在左侧选择一个基金代码,然后点击'运行分析'按钮.")
else:
    # 显示图表
    ts_code = list(st.session_state.charts.keys())[0]
    file_path = st.session_state.charts[ts_code]
    title_name = available_funds.get(ts_code, "自定义基金")
    st.subheader(f"{ts_code}: {title_name}")

    try:
        from streamlit.components.v1 import html
        with open(file_path, "r", encoding="utf-8") as f:
            html_content = f.read()
        html(html_content, height=750, scrolling=True)
    except Exception as e:
        st.error(f"加载图表失败: {str(e)}")

# 页脚
st.markdown("---")
st.caption("基金数据技术分析工具 © 2025")
