set hive.exec.dynamic.partition.mode=nonstrict;
set hive.exec.max.dynamic.partitions=60000;
set hive.exec.max.dynamic.partitions.pernode=60000;
set mapreduce.map.memory.mb=65536;
set mapreduce.reduce.memory.mb=65536;
set mapreduce.map.java.opts=-Xmx52428m;
set mapreduce.reduce.java.opts=-Xmx52428m;
SET hive.merge.mapfiles = true;
SET hive.merge.mapredfiles = true;
SET hive.merge.size.per.task = 256000000; 
SET hive.merge.smallfiles.avgsize = 256000000; 


kinit -kt ./ETL/keytab/hive.keytab hive
hive -e "set hive.exec.dynamic.partition.mode=nonstrict;
set hive.exec.max.dynamic.partitions=60000;
set hive.exec.max.dynamic.partitions.pernode=60000;
set mapreduce.map.memory.mb=65536;
set mapreduce.reduce.memory.mb=65536;
set mapreduce.map.java.opts=-Xmx52428m;
set mapreduce.reduce.java.opts=-Xmx52428m;
SET hive.merge.mapfiles = true;
SET hive.merge.mapredfiles = true;
SET hive.merge.size.per.task = 256000000; 
SET hive.merge.smallfiles.avgsize = 256000000; 
insert overwrite TABLE ods_jy.lc_balancesheetall partition(part_column) SELECT *,init_date_timestamp as part_column from ods_jy.stg_lc_balancesheetall DISTRIBUTE by (part_column,ceil(rand()*1));"

insert overwrite TABLE ddcrm.tcpsxsr_r partition(part_column) select '_id',yyb,khh,khh_xc,cpdm,rq,lx,cpsr,gxsj,cplx,jys,init_date_timestamp,part_column from ddcrm.stg_tcpsxsr_r;


-- 减少小文件
insert overwrite TABLE ods_tggz.vjk_wbfk_gzb partition(part_column) select '_id',l_bh,vc_kmdm,vc_kmmc,l_sl,en_dwcb,en_cb,en_cbzjz,en_hqjz,en_sz,en_szzjz,en_gzzz,d_ywrq,l_kind,vc_jsbz,en_exch,en_wbcb,en_wbhq,en_wbsz,vc_bzw,vc_cpdm,vc_scdm,l_sclb,l_ztbh,l_zqnm,d_scsj,l_sfqr,vc_qyxx,vc_tpxx,init_date_timestamp,part_column from ods_tggz.stg_vjk_wbfk_gzb DISTRIBUTE BY (part_column,ceil(rand()*5));

insert overwrite TABLE ddcrm.tkhjrcpbyl_r partition(part_column) select rq, khh, yyb, ry, byl, cpdm, lsbyl, cpsl, zxsz, byfe, bz, khh_xc, jszh, lx, jys, byfe_sr, cpsl_sr, init_date_timestamp,part_column from ddcrm.stg_tkhjrcpbyl_r;







