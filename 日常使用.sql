1.1 集群认证登录
学院路VPN+学院路堡垒机,选择 ************
新集群进行操作时需要进行kerberos认证:
用户:appdc  (对应Hive库dc用户)
权限:所有用户数据的读权限,Hive下dc用户的写权限
认证文件:************:/home/<USER>/appdc.ketab
认证方式:kinit -kt /home/<USER>/appdc.ketab
默认队列:root.yarn_appdc
1.2 Hive登录
在kerberos认证成功之后,使用beeline方式进行hive库登录
beeline -u '***************************************************************'

beeline -u '***************************************************************' -e "select count(1) from report.ads_cm_assmt_profit_mi";
--------------
防火墙策略开通
***********-50;***********-190;************-126
------------------
presto账号密码
wanggd
d3hx6t
------------------
服务器124 ctzqxy296700!
---------------------------------------
TO_CHAR(D_DATE,'yyyyMMdd')  > 20220101

operatedate = TO_CHAR((current_date-1),'YYYY-MM-DD')

-- #从四个月前的第一天开始同步数据
to_char(d_ywrq,'yyyyMMdd') >=TO_CHAR((last_day(ADD_MONTHS(SYSDATE,-5))+1),'YYYYMMDD')

-- #同步近一个月数据
TO_CHAR(D_DATE,'yyyyMMdd') >TO_NUMBER(TO_CHAR(ADD_MONTHS(SYSDATE,-1),'YYYYMMDD'))

#同步逻辑:取两个月之前的时间点,增量更新,需要设置主键
OC_DATE>TO_NUMBER(TO_CHAR(ADD_MONTHS(SYSDATE,-2),'YYYYMMDD')) and "OC_DATE" > 'last_max(OC_DATE)'

TradingDay>='2022-01-01 00:00:00.0'

D_YWRQ <'2021-01-01 00:00:00.0'  

Curr_Datetime>= to_date('********','yyyyMMdd')

"OPDATE" > timestamp 'last_max(OPDATE)'

OPDATE > TO_DATE( '2022-06-06 00:00:00','yyyy/mm/dd hh24:mi:ss')

#TACCOUNTZQJC
TO_CHAR(D_DATE,'yyyyMMdd') >TO_NUMBER(TO_CHAR(ADD_MONTHS(SYSDATE,-1),'YYYYMMDD')) 

TO_CHAR(SYSDATE - interval '15' day,'YYYYMMDD')

#CRM
RQ>=TO_NUMBER(TO_CHAR(ADD_MONTHS(SYSDATE,-2),'YYYYMMDD'))
---------------------------------------------------------
[root@xy-dsjpt-server01 opt]# echo $DISPLAY
***********:11.0
[root@xy-dsjpt-server01 opt]# export DISPLAY=***********:4.0
[root@xy-dsjpt-server01 opt]# echo $DISPLAY
***********:6.0
[root@xy-dsjpt-server01 opt]# cd /opt/kettle8.1/
[root@xy-dsjpt-server01 kettle8.1]# ./spoon.sh


-----------------------------------------

0 <USER> <GROUP>,11,13 * * ?     
0 0 3 * * ? 
--------------------------------
**********/jygldb02
CT_BIGDATA
ctzqbj262700!

31-50密码 Datapipeline123

***********-mysql	ctzqbj262700
--------------------------------
ctadmin
ctzqxy242500@
--------------------------------
**********
wanggd
暂时密码:Ctzq123456

-----------------------------------
数据校验
python37 /opt/py_venv/py_mobydq/dq_scripts/dqc_scripts_test.py --action exec --indgrp 32 --indlist '299,300'

>>> 加了一个 --indlist


bigdata_algo  对应是  python3 /opt/py_venv/
dataops-worker  对应是  python37 /opt/py_venv/


equ_fancy_xxx 精品因子库

-------------------------------------
drop view ddcrm.tcpsxsr_r;
-- 赋权

GRANT SELECT ON CT_ODS_DC.ADS_CLIENT_LABEL_BASE_INFO_DD TO CT_OUT_FERM;
REVOKE SELECT ON CT_ODS_NHR.MD_EMPLOYEE TO CT_OUT_QMFX;
GRANT SELECT ON CT_ODS_CW65.V_ZZFZHS TO CT_OUT_QMFX;
GRANT SELECT ON CT_ODS_UF20.HIS_TRANSFERENTRUST TO CT_OUT_ZYGZ
GRANT SELECT ON CT_ODS_CW65.V_HR_COMDEPT TO CT_OUT_YYCW
GRANT SELECT ON CT_ODS_CW65.ORG_ORGS TO CT_OUT_YYCW
GRANT SELECT ON CT_ODS_ZYYX.T_FUND_INFO TO CT_OUT_TGBS;
GRANT SELECT ON CT_ODS_ZYYX.T_FUND_ORG TO CT_OUT_TGBS;

----------------------------

ALTER TABLE datamgmt.ods_lr_mgmt_cs_report DROP PARTITION (part_column <='20221212')


CREATE view ddcrm.tcpsxsr_r AS
SELECT  *
FROM ddcrm.tcpsxsr_r_2020
UNION ALL
SELECT  *
FROM ddcrm.tcpsxsr_r_2021
UNION ALL
SELECT  *
FROM ddcrm.tcpsxsr_r_2022;


CREATE view dc.ods_uf20_his_srpcontractjour AS
SELECT  *
FROM uf20.his_srpcontractjour;


CREATE view dc.ods_jy_qt_performance AS
SELECT  *
FROM dc.ods_jy_qt_performance_2020
UNION ALL
SELECT  *
FROM dc.ods_jy_qt_performance_2021
UNION ALL
SELECT  *
FROM dc.ods_jy_qt_performance_2022
UNION ALL
SELECT  *
FROM dc.ods_jy_qt_performance_2023;


CREATE view dc.ods_jy_qt_hkdailyquote_view AS
SELECT  *
FROM dc.ods_jy_qt_hkdailyquote_2021
UNION ALL
SELECT  *
FROM dc.ods_jy_qt_hkdailyquote_2022
UNION ALL
SELECT  *
FROM dc.ods_jy_qt_hkdailyquote_2023;




CREATE view dc.ods_tggz_ttmp_h_gzb AS
SELECT  *
FROM ods_tggz.ttmp_h_gzb_new
UNION ALL
SELECT  *
FROM ods_tggz.ttmp_h_gzb_old;


CREATE VIEW `dc.ods_tggz_ttmp_h_gzb` AS SELECT  `ttmp_h_gzb_new`.`l_bh`, `ttmp_h_gzb_new`.`vc_kmdm`, `ttmp_h_gzb_new`.`vc_kmmc`, `ttmp_h_gzb_new`.`l_sl`, `ttmp_h_gzb_new`.`en_dwcb`, `ttmp_h_gzb_new`.`en_cb`, `ttmp_h_gzb_new`.`en_cbzjz`, `ttmp_h_gzb_new`.`en_hqjz`, `ttmp_h_gzb_new`.`en_sz`, `ttmp_h_gzb_new`.`en_szzjz`, `ttmp_h_gzb_new`.`en_gzzz`, `ttmp_h_gzb_new`.`vc_tpxx`, `ttmp_h_gzb_new`.`vc_qyxx`, `ttmp_h_gzb_new`.`l_tmpid`, `ttmp_h_gzb_new`.`vc_jys`, `ttmp_h_gzb_new`.`vc_tzpz`, `ttmp_h_gzb_new`.`vc_xjl`, `ttmp_h_gzb_new`.`vc_ts`, `ttmp_h_gzb_new`.`en_fdykbl`, `ttmp_h_gzb_new`.`l_leaf`, `ttmp_h_gzb_new`.`vc_kmparent`, `ttmp_h_gzb_new`.`l_level`, `ttmp_h_gzb_new`.`l_ztbh`, `ttmp_h_gzb_new`.`d_ywrq`, `ttmp_h_gzb_new`.`d_scsj`, `ttmp_h_gzb_new`.`l_quantity`, `ttmp_h_gzb_new`.`vc_code_hs`, `ttmp_h_gzb_new`.`l_kind`, `ttmp_h_gzb_new`.`l_gzkmbz`, `ttmp_h_gzb_new`.`vc_jsbz`, `ttmp_h_gzb_new`.`en_exch`, `ttmp_h_gzb_new`.`en_wbcb`, `ttmp_h_gzb_new`.`en_wbhq`, `ttmp_h_gzb_new`.`en_wbsz`, `ttmp_h_gzb_new`.`l_zqnm`, `ttmp_h_gzb_new`.`l_sfqr`, `ttmp_h_gzb_new`.`l_tzlx`, `ttmp_h_gzb_new`.`en_zyj`, `ttmp_h_gzb_new`.`en_wbzyj`, `ttmp_h_gzb_new`.`vc_checker`, `ttmp_h_gzb_new`.`vc_bzw`, `ttmp_h_gzb_new`.`vc_ltlx`, `ttmp_h_gzb_new`.`en_ybgzzz`, `ttmp_h_gzb_new`.`vc_zqdm`, `ttmp_h_gzb_new`.`l_sclb`, `ttmp_h_gzb_new`.`l_zqlb`, `ttmp_h_gzb_new`.`en_qyxx`, `ttmp_h_gzb_new`.`d_tpxx`, `ttmp_h_gzb_new`.`en_cezjz`
FROM `ods_tggz`.`ttmp_h_gzb_new`
UNION ALL
SELECT  `ttmp_h_gzb_old`.`l_bh`, `ttmp_h_gzb_old`.`vc_kmdm`, `ttmp_h_gzb_old`.`vc_kmmc`, `ttmp_h_gzb_old`.`l_sl`, `ttmp_h_gzb_old`.`en_dwcb`, `ttmp_h_gzb_old`.`en_cb`, `ttmp_h_gzb_old`.`en_cbzjz`, `ttmp_h_gzb_old`.`en_hqjz`, `ttmp_h_gzb_old`.`en_sz`, `ttmp_h_gzb_old`.`en_szzjz`, `ttmp_h_gzb_old`.`en_gzzz`, `ttmp_h_gzb_old`.`vc_tpxx`, `ttmp_h_gzb_old`.`vc_qyxx`, `ttmp_h_gzb_old`.`l_tmpid`, `ttmp_h_gzb_old`.`vc_jys`, `ttmp_h_gzb_old`.`vc_tzpz`, `ttmp_h_gzb_old`.`vc_xjl`, `ttmp_h_gzb_old`.`vc_ts`, `ttmp_h_gzb_old`.`en_fdykbl`, `ttmp_h_gzb_old`.`l_leaf`, `ttmp_h_gzb_old`.`vc_kmparent`, `ttmp_h_gzb_old`.`l_level`, `ttmp_h_gzb_old`.`l_ztbh`, `ttmp_h_gzb_old`.`d_ywrq`, `ttmp_h_gzb_old`.`d_scsj`, `ttmp_h_gzb_old`.`l_quantity`, `ttmp_h_gzb_old`.`vc_code_hs`, `ttmp_h_gzb_old`.`l_kind`, `ttmp_h_gzb_old`.`l_gzkmbz`, `ttmp_h_gzb_old`.`vc_jsbz`, `ttmp_h_gzb_old`.`en_exch`, `ttmp_h_gzb_old`.`en_wbcb`, `ttmp_h_gzb_old`.`en_wbhq`, `ttmp_h_gzb_old`.`en_wbsz`, `ttmp_h_gzb_old`.`l_zqnm`, `ttmp_h_gzb_old`.`l_sfqr`, `ttmp_h_gzb_old`.`l_tzlx`, `ttmp_h_gzb_old`.`en_zyj`, `ttmp_h_gzb_old`.`en_wbzyj`, `ttmp_h_gzb_old`.`vc_checker`, `ttmp_h_gzb_old`.`vc_bzw`, `ttmp_h_gzb_old`.`vc_ltlx`, `ttmp_h_gzb_old`.`en_ybgzzz`, `ttmp_h_gzb_old`.`vc_zqdm`, `ttmp_h_gzb_old`.`l_sclb`, `ttmp_h_gzb_old`.`l_zqlb`, `ttmp_h_gzb_old`.`en_qyxx`, `ttmp_h_gzb_old`.`d_tpxx`, `ttmp_h_gzb_old`.`en_cezjz`
FROM `ods_tggz`.`ttmp_h_gzb_old`





CREATE view dc.ods_uf20_his_optriskacctlist AS
SELECT  *
FROM uf20.his_optriskacctlist;





CREATE OR REPLACE VIEW CT_ODS_TGGZ.VJK_WBFK_GZB AS
SELECT * FROM CT_ODS_TGGZ.VJK_WBFK_GZB_CLOD
union all
SELECT * FROM CT_ODS_TGGZ.VJK_WBFK_GZB_HOT;

CREATE OR REPLACE VIEW dc.ods_gmtg_its_his_comb_request AS select * from ods_gmtg.its_his_comb_request;



CREATE OR REPLACE VIEW CT_ODS_TGGZ.VJK_WBFK_VOUCHERS AS
SELECT * FROM CT_ODS_TGGZ.VJK_WBFK_VOUCHERS_CLOD
union all
SELECT * FROM CT_ODS_TGGZ.VJK_WBFK_VOUCHERS_HOT;



CREATE view dc.ods_mysql_organcust_ctzg_agency_info_dd AS
SELECT  *
FROM ods_mysql.organcust_ctzg_agency_info_dd;

CREATE VIEW `dc.ods_mysql_ct_innovation` AS SELECT  `ct_innovation`. `ct_innovation`.`company_name_old`, `ct_innovation`.`credit_code`, `ct_innovation`.`update_time`, `ct_innovation`.`id`
FROM `ods_mysql`.`organcust_ctzg_agency_info_dd`


CREATE OR REPLACE VIEW CT_ODS_ZGGZ_TM.TTMP_H_GZB AS
SELECT * FROM CT_ODS_ZGGZ_TM.VTTMP_H_GZB_CLOD
union all
SELECT * FROM CT_ODS_ZGGZ_TM.TTMP_H_GZB_HOT;




CREATE OR REPLACE VIEW CT_ODS_TGGZ.TVOUCHERS AS
SELECT * FROM CT_ODS_TGGZ.TVOUCHERS_NEW
union all
SELECT * FROM CT_ODS_TGGZ.TVOUCHERS_OLD;








CREATE VIEW `ddcrm.tcpsxsr_r` AS 
select `tcpsxsr_r_2020`.`_id`,
`tcpsxsr_r_2020`.`yyb`, `tcpsxsr_r_2020`.`khh`, `tcpsxsr_r_2020`.`khh_xc`,
`tcpsxsr_r_2020`.`cpdm`, `tcpsxsr_r_2020`.`rq`, `tcpsxsr_r_2020`.`lx`,
`tcpsxsr_r_2020`.`cpsr`, `tcpsxsr_r_2020`.`gxsj`, `tcpsxsr_r_2020`.`cplx`,
`tcpsxsr_r_2020`.`jys`, `tcpsxsr_r_2020`.`init_date_timestamp`,
`tcpsxsr_r_2020`.`part_column` from `ddcrm`.`tcpsxsr_r_2020` 
union all
 select
`tcpsxsr_r_2021`.`_id`, `tcpsxsr_r_2021`.`yyb`, `tcpsxsr_r_2021`.`khh`,
`tcpsxsr_r_2021`.`khh_xc`, `tcpsxsr_r_2021`.`cpdm`, `tcpsxsr_r_2021`.`rq`,
`tcpsxsr_r_2021`.`lx`, `tcpsxsr_r_2021`.`cpsr`, `tcpsxsr_r_2021`.`gxsj`,
`tcpsxsr_r_2021`.`cplx`, `tcpsxsr_r_2021`.`jys`,
`tcpsxsr_r_2021`.`init_date_timestamp`, `tcpsxsr_r_2021`.`part_column` from
`ddcrm`.`tcpsxsr_r_2021` 
union all 
select `tcpsxsr_r_2022`.`_id`,
`tcpsxsr_r_2022`.`yyb`, `tcpsxsr_r_2022`.`khh`, `tcpsxsr_r_2022`.`khh_xc`,
`tcpsxsr_r_2022`.`cpdm`, `tcpsxsr_r_2022`.`rq`, `tcpsxsr_r_2022`.`lx`,
`tcpsxsr_r_2022`.`cpsr`, `tcpsxsr_r_2022`.`gxsj`, `tcpsxsr_r_2022`.`cplx`,
`tcpsxsr_r_2022`.`jys`, `tcpsxsr_r_2022`.`init_date_timestamp`,
`tcpsxsr_r_2022`.`part_column` from `ddcrm`.`tcpsxsr_r_2022`


alter table ddcrm.stg_tcpsxsr_r drop partition (part_column>=1667059200000,part_column<20221107);

CREATE OR REPLACE VIEW dc.ods_tggz_v_all_tvouchers AS SELECT * FROM  ods_tggz.v_all_tvouchers;
CREATE OR REPLACE VIEW dc.ods_crm_i_bigdt_zb_ry_cp_y AS SELECT * FROM  ddcrm.i_bigdt_zb_ry_cp_y;

CREATE OR REPLACE VIEW dc.ods_wind_windcustomcode_delta AS SELECT * FROM dc.ods_wind_windcustomcode;


CREATE OR REPLACE VIEW `dc.ods_tggz_v_all_tvouchers` AS SELECT `v_all_tvouchers`.`l_id`, `v_all_tvouchers`.`l_fundid`, `v_all_tvouchers`.`l_mainid`, `v_all_tvouchers`.`l_czbzid`, `v_all_tvouchers`.`l_row`, `v_all_tvouchers`.`vc_digest`, `v_all_tvouchers`.`vc_code`, `v_all_tvouchers`.`vc_fullname`, `v_all_tvouchers`.`en_debit`, `v_all_tvouchers`.`en_credit`, `v_all_tvouchers`.`en_foreign`, `v_all_tvouchers`.`en_exch`, `v_all_tvouchers`.`en_quantity`, `v_all_tvouchers`.`en_price`, `v_all_tvouchers`.`vc_department`, `v_all_tvouchers`.`vc_cash`, `v_all_tvouchers`.`vc_insurance`, `v_all_tvouchers`.`vc_charge`, `v_all_tvouchers`.`vc_bourse`, `v_all_tvouchers`.`vc_days`, `v_all_tvouchers`.`vc_investacc`, `v_all_tvouchers`.`vc_settleno`, `v_all_tvouchers`.`vc_costapportion`, `v_all_tvouchers`.`l_tzlx`, `v_all_tvouchers`.`l_zqnm`, `v_all_tvouchers`.`vc_code_hs`, `v_all_tvouchers`.`vc_jsbz`, `v_all_tvouchers`.`d_make`, `v_all_tvouchers`.`en_tradeamo`, `v_all_tvouchers`.`en_fortradeamo`, `v_all_tvouchers`.`l_state`, `v_all_tvouchers`.`l_cchsid`, `v_all_tvouchers`.`d_date`, `v_all_tvouchers`.`l_year`, `v_all_tvouchers`.`l_month`, `v_all_tvouchers`.`init_date_timestamp`, `v_all_tvouchers`.`part_column` FROM  `ods_tggz`.`v_all_tvouchers`

CREATE OR REPLACE VIEW `dc.ods_tggz_v_all_tvouchers` AS SELECT  `ods_tggz_v_all_tvouchers_2020`.`l_id`, `ods_tggz_v_all_tvouchers_2020`.`l_fundid`, `ods_tggz_v_all_tvouchers_2020`.`l_mainid`, `ods_tggz_v_all_tvouchers_2020`.`l_czbzid`, `ods_tggz_v_all_tvouchers_2020`.`l_row`, `ods_tggz_v_all_tvouchers_2020`.`vc_digest`, `ods_tggz_v_all_tvouchers_2020`.`vc_code`, `ods_tggz_v_all_tvouchers_2020`.`vc_fullname`, `ods_tggz_v_all_tvouchers_2020`.`en_debit`, `ods_tggz_v_all_tvouchers_2020`.`en_credit`, `ods_tggz_v_all_tvouchers_2020`.`en_foreign`, `ods_tggz_v_all_tvouchers_2020`.`en_exch`, `ods_tggz_v_all_tvouchers_2020`.`en_quantity`, `ods_tggz_v_all_tvouchers_2020`.`en_price`, `ods_tggz_v_all_tvouchers_2020`.`vc_department`, `ods_tggz_v_all_tvouchers_2020`.`vc_cash`, `ods_tggz_v_all_tvouchers_2020`.`vc_insurance`, `ods_tggz_v_all_tvouchers_2020`.`vc_charge`, `ods_tggz_v_all_tvouchers_2020`.`vc_bourse`, `ods_tggz_v_all_tvouchers_2020`.`vc_days`, `ods_tggz_v_all_tvouchers_2020`.`vc_investacc`, `ods_tggz_v_all_tvouchers_2020`.`vc_settleno`, `ods_tggz_v_all_tvouchers_2020`.`vc_costapportion`, `ods_tggz_v_all_tvouchers_2020`.`l_tzlx`, `ods_tggz_v_all_tvouchers_2020`.`l_zqnm`, `ods_tggz_v_all_tvouchers_2020`.`vc_code_hs`, `ods_tggz_v_all_tvouchers_2020`.`vc_jsbz`, `ods_tggz_v_all_tvouchers_2020`.`d_make`, `ods_tggz_v_all_tvouchers_2020`.`en_tradeamo`, `ods_tggz_v_all_tvouchers_2020`.`en_fortradeamo`, `ods_tggz_v_all_tvouchers_2020`.`l_state`, `ods_tggz_v_all_tvouchers_2020`.`l_cchsid`, `ods_tggz_v_all_tvouchers_2020`.`d_date`, `ods_tggz_v_all_tvouchers_2020`.`l_year`, `ods_tggz_v_all_tvouchers_2020`.`l_month`
FROM `dc`.`ods_tggz_v_all_tvouchers_2020`
UNION ALL
SELECT  `ods_tggz_v_all_tvouchers_2021`.`l_id`, `ods_tggz_v_all_tvouchers_2021`.`l_fundid`, `ods_tggz_v_all_tvouchers_2021`.`l_mainid`, `ods_tggz_v_all_tvouchers_2021`.`l_czbzid`, `ods_tggz_v_all_tvouchers_2021`.`l_row`, `ods_tggz_v_all_tvouchers_2021`.`vc_digest`, `ods_tggz_v_all_tvouchers_2021`.`vc_code`, `ods_tggz_v_all_tvouchers_2021`.`vc_fullname`, `ods_tggz_v_all_tvouchers_2021`.`en_debit`, `ods_tggz_v_all_tvouchers_2021`.`en_credit`, `ods_tggz_v_all_tvouchers_2021`.`en_foreign`, `ods_tggz_v_all_tvouchers_2021`.`en_exch`, `ods_tggz_v_all_tvouchers_2021`.`en_quantity`, `ods_tggz_v_all_tvouchers_2021`.`en_price`, `ods_tggz_v_all_tvouchers_2021`.`vc_department`, `ods_tggz_v_all_tvouchers_2021`.`vc_cash`, `ods_tggz_v_all_tvouchers_2021`.`vc_insurance`, `ods_tggz_v_all_tvouchers_2021`.`vc_charge`, `ods_tggz_v_all_tvouchers_2021`.`vc_bourse`, `ods_tggz_v_all_tvouchers_2021`.`vc_days`, `ods_tggz_v_all_tvouchers_2021`.`vc_investacc`, `ods_tggz_v_all_tvouchers_2021`.`vc_settleno`, `ods_tggz_v_all_tvouchers_2021`.`vc_costapportion`, `ods_tggz_v_all_tvouchers_2021`.`l_tzlx`, `ods_tggz_v_all_tvouchers_2021`.`l_zqnm`, `ods_tggz_v_all_tvouchers_2021`.`vc_code_hs`, `ods_tggz_v_all_tvouchers_2021`.`vc_jsbz`, `ods_tggz_v_all_tvouchers_2021`.`d_make`, `ods_tggz_v_all_tvouchers_2021`.`en_tradeamo`, `ods_tggz_v_all_tvouchers_2021`.`en_fortradeamo`, `ods_tggz_v_all_tvouchers_2021`.`l_state`, `ods_tggz_v_all_tvouchers_2021`.`l_cchsid`, `ods_tggz_v_all_tvouchers_2021`.`d_date`, `ods_tggz_v_all_tvouchers_2021`.`l_year`, `ods_tggz_v_all_tvouchers_2021`.`l_month`
FROM `dc`.`ods_tggz_v_all_tvouchers_2021`
UNION ALL
SELECT  `ods_tggz_v_all_tvouchers_2022`.`l_id`, `ods_tggz_v_all_tvouchers_2022`.`l_fundid`, `ods_tggz_v_all_tvouchers_2022`.`l_mainid`, `ods_tggz_v_all_tvouchers_2022`.`l_czbzid`, `ods_tggz_v_all_tvouchers_2022`.`l_row`, `ods_tggz_v_all_tvouchers_2022`.`vc_digest`, `ods_tggz_v_all_tvouchers_2022`.`vc_code`, `ods_tggz_v_all_tvouchers_2022`.`vc_fullname`, `ods_tggz_v_all_tvouchers_2022`.`en_debit`, `ods_tggz_v_all_tvouchers_2022`.`en_credit`, `ods_tggz_v_all_tvouchers_2022`.`en_foreign`, `ods_tggz_v_all_tvouchers_2022`.`en_exch`, `ods_tggz_v_all_tvouchers_2022`.`en_quantity`, `ods_tggz_v_all_tvouchers_2022`.`en_price`, `ods_tggz_v_all_tvouchers_2022`.`vc_department`, `ods_tggz_v_all_tvouchers_2022`.`vc_cash`, `ods_tggz_v_all_tvouchers_2022`.`vc_insurance`, `ods_tggz_v_all_tvouchers_2022`.`vc_charge`, `ods_tggz_v_all_tvouchers_2022`.`vc_bourse`, `ods_tggz_v_all_tvouchers_2022`.`vc_days`, `ods_tggz_v_all_tvouchers_2022`.`vc_investacc`, `ods_tggz_v_all_tvouchers_2022`.`vc_settleno`, `ods_tggz_v_all_tvouchers_2022`.`vc_costapportion`, `ods_tggz_v_all_tvouchers_2022`.`l_tzlx`, `ods_tggz_v_all_tvouchers_2022`.`l_zqnm`, `ods_tggz_v_all_tvouchers_2022`.`vc_code_hs`, `ods_tggz_v_all_tvouchers_2022`.`vc_jsbz`, `ods_tggz_v_all_tvouchers_2022`.`d_make`, `ods_tggz_v_all_tvouchers_2022`.`en_tradeamo`, `ods_tggz_v_all_tvouchers_2022`.`en_fortradeamo`, `ods_tggz_v_all_tvouchers_2022`.`l_state`, `ods_tggz_v_all_tvouchers_2022`.`l_cchsid`, `ods_tggz_v_all_tvouchers_2022`.`d_date`, `ods_tggz_v_all_tvouchers_2022`.`l_year`, `ods_tggz_v_all_tvouchers_2022`.`l_month`
FROM `dc`.`ods_tggz_v_all_tvouchers_2022`
UNION ALL
SELECT  `ods_tggz_v_all_tvouchers_2023`.`l_id`, `ods_tggz_v_all_tvouchers_2023`.`l_fundid`, `ods_tggz_v_all_tvouchers_2023`.`l_mainid`, `ods_tggz_v_all_tvouchers_2023`.`l_czbzid`, `ods_tggz_v_all_tvouchers_2023`.`l_row`, `ods_tggz_v_all_tvouchers_2023`.`vc_digest`, `ods_tggz_v_all_tvouchers_2023`.`vc_code`, `ods_tggz_v_all_tvouchers_2023`.`vc_fullname`, `ods_tggz_v_all_tvouchers_2023`.`en_debit`, `ods_tggz_v_all_tvouchers_2023`.`en_credit`, `ods_tggz_v_all_tvouchers_2023`.`en_foreign`, `ods_tggz_v_all_tvouchers_2023`.`en_exch`, `ods_tggz_v_all_tvouchers_2023`.`en_quantity`, `ods_tggz_v_all_tvouchers_2023`.`en_price`, `ods_tggz_v_all_tvouchers_2023`.`vc_department`, `ods_tggz_v_all_tvouchers_2023`.`vc_cash`, `ods_tggz_v_all_tvouchers_2023`.`vc_insurance`, `ods_tggz_v_all_tvouchers_2023`.`vc_charge`, `ods_tggz_v_all_tvouchers_2023`.`vc_bourse`, `ods_tggz_v_all_tvouchers_2023`.`vc_days`, `ods_tggz_v_all_tvouchers_2023`.`vc_investacc`, `ods_tggz_v_all_tvouchers_2023`.`vc_settleno`, `ods_tggz_v_all_tvouchers_2023`.`vc_costapportion`, `ods_tggz_v_all_tvouchers_2023`.`l_tzlx`, `ods_tggz_v_all_tvouchers_2023`.`l_zqnm`, `ods_tggz_v_all_tvouchers_2023`.`vc_code_hs`, `ods_tggz_v_all_tvouchers_2023`.`vc_jsbz`, `ods_tggz_v_all_tvouchers_2023`.`d_make`, `ods_tggz_v_all_tvouchers_2023`.`en_tradeamo`, `ods_tggz_v_all_tvouchers_2023`.`en_fortradeamo`, `ods_tggz_v_all_tvouchers_2023`.`l_state`, `ods_tggz_v_all_tvouchers_2023`.`l_cchsid`, `ods_tggz_v_all_tvouchers_2023`.`d_date`, `ods_tggz_v_all_tvouchers_2023`.`l_year`, `ods_tggz_v_all_tvouchers_2023`.`l_month`
FROM `dc`.`ods_tggz_v_all_tvouchers_2023`;


CREATE OR REPLACE VIEW CT_ODS_TGGZ.TACCOUNTZQJC AS
SELECT  *
FROM CT_ODS_TGGZ.TACCOUNTZQJC_NEW
UNION ALL
SELECT  *
FROM CT_ODS_TGGZ.TACCOUNTZQJC_OLD


create or replace view ods_uf30.act_phonenum_place as select a.* from ods_uf30_daily.act_phonenum_place_daily a join (select max(part_column) as max_part from ods_uf30_daily.sps_seats_daily) b on a.part_column = b.max_part 