--账户分析新标志位
-- 插入每张表日期
--场内

ADS_STOCK_PROFIT_DAY_DI_NORM
ADS_STOCK_PROFIT_CLEAR_DI_NORM
ADS_STOCK_PROFIT_MONTH_MI_NORM
ADS_STOCK_PROFIT_YEAR_YI_NORM
ADS_STOCK_PROFIT_TD_YF_NORM


INSERT INTO TABLE_JOB_STATUS (ID,TABLE_NAME, MAX_INIT_DATE, UPDATEDATE, STATUS)  
SELECT '1' as ID,'ADS_STOCK_PROFIT_DAY_DI_NORM' AS TABLE_NAME, MAX(INIT_DATE) AS MAX_INIT_DATE ,TO_CHAR(SYSDATE, 'YYYYMMDD') AS UPDATEDATE,1 AS STATUS FROM ADS_STOCK_PROFIT_DAY_DI_NORM;
INSERT INTO TABLE_JOB_STATUS (ID,TABLE_NAME, MAX_INIT_DATE, UPDATEDATE, STATUS)  
SELECT '2' as ID,'ADS_STOCK_PROFIT_CLEAR_DI_NORM' AS TABLE_NAME, MAX(INIT_DATE) AS MAX_INIT_DATE ,TO_CHAR(SYSDATE, 'YYYYMMDD') AS UPDATEDATE,1 AS STATUS FROM ADS_STOCK_PROFIT_CLEAR_DI_NORM;
INSERT INTO TABLE_JOB_STATUS (ID,TABLE_NAME, MAX_INIT_DATE, UPDATEDATE, STATUS)  
SELECT '3' as ID,'ADS_STOCK_PROFIT_MONTH_MI_NORM' AS TABLE_NAME, MAX(INIT_DATE) AS MAX_INIT_DATE ,TO_CHAR(SYSDATE, 'YYYYMMDD') AS UPDATEDATE,1 AS STATUS FROM ADS_STOCK_PROFIT_MONTH_MI_NORM;
INSERT INTO TABLE_JOB_STATUS (ID,TABLE_NAME, MAX_INIT_DATE, UPDATEDATE, STATUS)  
SELECT '4' as ID,'ADS_STOCK_PROFIT_YEAR_YI_NORM' AS TABLE_NAME, MAX(INIT_DATE) AS MAX_INIT_DATE ,TO_CHAR(SYSDATE, 'YYYYMMDD') AS UPDATEDATE,1 AS STATUS FROM ADS_STOCK_PROFIT_YEAR_YI_NORM;
INSERT INTO TABLE_JOB_STATUS (ID,TABLE_NAME, MAX_INIT_DATE, UPDATEDATE, STATUS)  
SELECT '5' as ID,'ADS_STOCK_PROFIT_TD_YF_NORM' AS TABLE_NAME, MAX(INIT_DATE) AS MAX_INIT_DATE ,TO_CHAR(SYSDATE, 'YYYYMMDD') AS UPDATEDATE,1 AS STATUS FROM ADS_STOCK_PROFIT_TD_YF_NORM;

--场内-状态位合并-READY_DATE_IN
INSERT INTO TABLE_JOB_STATUS (ID,TABLE_NAME, MAX_INIT_DATE, UPDATEDATE, STATUS)
SELECT '101' as ID,'READY_DATE_IN', max(MAX_INIT_DATE),TO_CHAR(SYSDATE, 'YYYYMMDD'),1  FROM (
SELECT MAX_INIT_DATE, count( DISTINCT id ) FROM TABLE_JOB_STATUS tjs
WHERE UPDATEDATE >= TO_CHAR(SYSDATE, 'YYYYMMDD') and ID in ('1','2','3','4','5')
GROUP BY MAX_INIT_DATE HAVING count( DISTINCT id ) >= 5
) t ;

--总账

ADS_NORM_ACC_PROFIT_DAY_DF
ADS_ACC_PROFIT_MONTH_MF_NORM
ADS_ACC_PROFIT_YEAR_YF_NORM
ADS_ACC_PROFIT_TD_YF_NORM

INSERT INTO TABLE_JOB_STATUS (ID,TABLE_NAME, MAX_INIT_DATE, UPDATEDATE, STATUS)  
SELECT '6' as ID,'ADS_NORM_ACC_PROFIT_DAY_DF' AS TABLE_NAME, MAX(INIT_DATE) AS MAX_INIT_DATE ,TO_CHAR(SYSDATE, 'YYYYMMDD') AS UPDATEDATE,1 AS STATUS FROM ADS_NORM_ACC_PROFIT_DAY_DF;
INSERT INTO TABLE_JOB_STATUS (ID,TABLE_NAME, MAX_INIT_DATE, UPDATEDATE, STATUS)  
SELECT '7' as ID,'ADS_ACC_PROFIT_MONTH_MF_NORM' AS TABLE_NAME, MAX(INIT_DATE) AS MAX_INIT_DATE ,TO_CHAR(SYSDATE, 'YYYYMMDD') AS UPDATEDATE,1 AS STATUS FROM ADS_ACC_PROFIT_MONTH_MF_NORM;
INSERT INTO TABLE_JOB_STATUS (ID,TABLE_NAME, MAX_INIT_DATE, UPDATEDATE, STATUS)  
SELECT '8' as ID,'ADS_ACC_PROFIT_YEAR_YF_NORM' AS TABLE_NAME, MAX(INIT_DATE) AS MAX_INIT_DATE ,TO_CHAR(SYSDATE, 'YYYYMMDD') AS UPDATEDATE,1 AS STATUS FROM ADS_ACC_PROFIT_YEAR_YF_NORM;
INSERT INTO TABLE_JOB_STATUS (ID,TABLE_NAME, MAX_INIT_DATE, UPDATEDATE, STATUS)  
SELECT '9' as ID,'ADS_ACC_PROFIT_TD_YF_NORM' AS TABLE_NAME, MAX(INIT_DATE) AS MAX_INIT_DATE ,TO_CHAR(SYSDATE, 'YYYYMMDD') AS UPDATEDATE,1 AS STATUS FROM ADS_ACC_PROFIT_TD_YF_NORM;


--总账-状态位合并-READY_DATE_GA
INSERT INTO TABLE_JOB_STATUS (ID,TABLE_NAME, MAX_INIT_DATE, UPDATEDATE, STATUS)
SELECT '102' as ID,'READY_DATE_GA', max(MAX_INIT_DATE),TO_CHAR(SYSDATE, 'YYYYMMDD'),1  FROM (
SELECT MAX_INIT_DATE, count( DISTINCT ID ) FROM TABLE_JOB_STATUS tjs
WHERE UPDATEDATE >= TO_CHAR(SYSDATE, 'YYYYMMDD') and ID in ('6','7','8','9')
GROUP BY MAX_INIT_DATE HAVING count( DISTINCT ID ) >= 4
) t ;



--场外
INSERT INTO TABLE_JOB_STATUS (ID,TABLE_NAME, MAX_INIT_DATE, UPDATEDATE, STATUS)
SELECT '1' as ID,'ADS_PROD_PROFIT_RATE_1M_DI' AS TABLE_NAME, MAX(INIT_DATE) AS MAX_INIT_DATE ,TO_CHAR(SYSDATE, 'YYYYMMDD') AS UPDATEDATE,1 AS STATUS FROM ADS_PROD_PROFIT_RATE_1M_DI;
INSERT INTO TABLE_JOB_STATUS (ID,TABLE_NAME, MAX_INIT_DATE, UPDATEDATE, STATUS)
SELECT '2' as ID,'ADS_PROD_PROFIT_RATE_1Y_DI' AS TABLE_NAME, MAX(INIT_DATE) AS MAX_INIT_DATE ,TO_CHAR(SYSDATE, 'YYYYMMDD') AS UPDATEDATE,1 AS STATUS FROM ADS_PROD_PROFIT_RATE_1Y_DI;
INSERT INTO TABLE_JOB_STATUS (ID,TABLE_NAME, MAX_INIT_DATE, UPDATEDATE, STATUS)
SELECT '3' as ID,'ADS_PROD_ACCOUNT_PROFIT_1M_DI' AS TABLE_NAME, MAX(INIT_DATE) AS MAX_INIT_DATE ,TO_CHAR(SYSDATE, 'YYYYMMDD') AS UPDATEDATE,1 AS STATUS FROM ADS_PROD_ACCOUNT_PROFIT_1M_DI;
INSERT INTO TABLE_JOB_STATUS (ID,TABLE_NAME, MAX_INIT_DATE, UPDATEDATE, STATUS)
SELECT '4' as ID,'ADS_PROD_ACCOUNT_PROFIT_1Y_DI' AS TABLE_NAME, MAX(INIT_DATE) AS MAX_INIT_DATE ,TO_CHAR(SYSDATE, 'YYYYMMDD') AS UPDATEDATE,1 AS STATUS FROM ADS_PROD_ACCOUNT_PROFIT_1Y_DI;
INSERT INTO TABLE_JOB_STATUS (ID,TABLE_NAME, MAX_INIT_DATE, UPDATEDATE, STATUS)
SELECT '5' as ID,'ADS_PROD_ACCOUNT_PROFIT_TD_DI' AS TABLE_NAME, MAX(INIT_DATE) AS MAX_INIT_DATE ,TO_CHAR(SYSDATE, 'YYYYMMDD') AS UPDATEDATE,1 AS STATUS FROM ADS_PROD_ACCOUNT_PROFIT_TD_DI;
INSERT INTO TABLE_JOB_STATUS (ID,TABLE_NAME, MAX_INIT_DATE, UPDATEDATE, STATUS)
SELECT '6' as ID,'ADS_PROD_PROFIT_DI' AS TABLE_NAME, MAX(INIT_DATE) AS MAX_INIT_DATE ,TO_CHAR(SYSDATE, 'YYYYMMDD') AS UPDATEDATE,1 AS STATUS FROM ADS_PROD_PROFIT_DI;
INSERT INTO TABLE_JOB_STATUS (ID,TABLE_NAME, MAX_INIT_DATE, UPDATEDATE, STATUS)
SELECT '7' as ID,'ADS_PROD_ACCOUNT_PROFIT_DI' AS TABLE_NAME, MAX(INIT_DATE) AS MAX_INIT_DATE ,TO_CHAR(SYSDATE, 'YYYYMMDD') AS UPDATEDATE,1 AS STATUS FROM ADS_PROD_ACCOUNT_PROFIT_DI;
INSERT INTO TABLE_JOB_STATUS (ID,TABLE_NAME, MAX_INIT_DATE, UPDATEDATE, STATUS)
SELECT '8' as ID,'ADS_PROD_PROFIT_MI' AS TABLE_NAME, MAX(INIT_DATE) AS MAX_INIT_DATE ,TO_CHAR(SYSDATE, 'YYYYMMDD') AS UPDATEDATE,1 AS STATUS FROM ADS_PROD_PROFIT_MI;
INSERT INTO TABLE_JOB_STATUS (ID,TABLE_NAME, MAX_INIT_DATE, UPDATEDATE, STATUS)
SELECT '9' as ID,'ADS_PROD_ACCOUNT_PROFIT_MI' AS TABLE_NAME, MAX(INIT_DATE) AS MAX_INIT_DATE ,TO_CHAR(SYSDATE, 'YYYYMMDD') AS UPDATEDATE,1 AS STATUS FROM ADS_PROD_ACCOUNT_PROFIT_MI;

--场外-状态位合并-READY_DATE_EX
INSERT INTO TABLE_JOB_STATUS (ID,TABLE_NAME, MAX_INIT_DATE, UPDATEDATE, STATUS)
SELECT '100' as ID,'READY_DATE_EX', max(MAX_INIT_DATE),TO_CHAR(SYSDATE, 'YYYYMMDD'),1  FROM (
SELECT MAX_INIT_DATE, count( DISTINCT ID ) FROM TABLE_JOB_STATUS tjs
WHERE UPDATEDATE >= TO_CHAR(SYSDATE, 'YYYYMMDD') and ID in ('1','2','3','4','5','6','7','8','9')
GROUP BY MAX_INIT_DATE HAVING count( DISTINCT ID ) >= 9
) t ;

-------------------------------
-- 查询接口
--场外-DM_ACCT_EX
SELECT MAX_INIT_DATE, status FROM TABLE_JOB_STATUS WHERE id=100 AND status = 1 and UPDATEDATE >= TO_CHAR(SYSDATE, 'YYYYMMDD');
--场内-DM_ACCT_IN
SELECT MAX_INIT_DATE, status FROM TABLE_JOB_STATUS WHERE id=101 AND status = 1 and UPDATEDATE >= TO_CHAR(SYSDATE, 'YYYYMMDD');
--总账-DM_ACCT_GA
SELECT MAX_INIT_DATE, status FROM TABLE_JOB_STATUS WHERE id=102 AND status = 1 and UPDATEDATE >= TO_CHAR(SYSDATE, 'YYYYMMDD');

--查询接口
场内
curl http://10.30.50.125:7011/dc_service_a/zhfx/dm_acct_in
总账
curl http://10.30.50.125:7011/dc_service_a/zhfx/dm_acct_ga
场外
curl http://10.30.50.125:7011/dc_service_a/zhfx/dm_acct_ex

http://10.30.50.177:6689/dc_service_a/zhfx/dm_acct_in
http://10.30.50.177:6689/dc_service_a/zhfx/dm_acct_ga
http://10.30.50.177:6689/dc_service_a/zhfx/dm_acct_ex

--测试环境标志位查询接口
场内
curl http://172.15.4.212:7011/dc_service_a/zhfx/dm_acct_in
总账
curl http://172.15.4.212:7011/dc_service_a/zhfx/dm_acct_ga
场外
curl http://172.15.4.212:7011/dc_service_a/zhfx/dm_acct_ex




