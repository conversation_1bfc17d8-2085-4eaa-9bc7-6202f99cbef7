--增加数据写入时间
,SYSDATE AS updatetime

-- 获取表状态
SELECT case when count(1) >0 THEN 1 ELSE 0 END as cnt
FROM RCISP.ADS_RCISP_AGG_CRM_BROKER_INFO_DD
WHERE updatetime >= DATE_FORMAT(CURDATE(), '%Y/%m/%d %H:%i:%s')




-- 1. 更新标志位表
INSERT INTO CT_ODS_DC.TABLE_JOB_STATUS
(
OWNER,
TABLE_NAME,
TABLE_COMMENT,
TABLE_CATEGORY,
STATUS,
BUSI_SDATE,
BUSI_EDATE,
STIME,
ETIME,
REMARK,
DATA_SOURCES
)
VALUES
(
  'RCISP',
  'ADS_RCISP_AGG_CRM_BROKER_INFO_DD',
  'CRM经纪人信息表',
  0,
  (CASE WHEN cnt > 0 THEN 1 ELSE 0 END),
  TO_CHAR(SYSDATE, 'yyyyMMdd'),
  TO_CHAR(SYSDAT<PERSON>, 'yyyyMMdd'),
  <PERSON>Y<PERSON><PERSON><PERSON>,
  <PERSON>Y<PERSON><PERSON><PERSON>,
  NULL,
  'CRM'
)



''
--接口
curl 'http://10.30.50.221:9201/dops/api/execute/v1/sysprepar/rcisp?accessToken=90307b3b3cad46729b14c453008a6af73bdd0c2ba24a4697d1e0e4e4502c6fce&table_name=ads_rcisp_agg_crm_salary_settle_info_dd'






