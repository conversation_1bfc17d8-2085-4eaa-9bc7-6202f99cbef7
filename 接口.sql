curl http://************:7011/dc_service_a/performance/ADS_CRM_CUST_PROD_SELL_DD
curl http://************:7011/dc_service_a/performance/DIM_PUB_BRANCH_DD

curl http://************:7011/dc_service_a/szth/ads_th_bond_iss_info_dd_del
curl http://************:7011/dc_service_a/szth/ads_th_bond_serv_trad_dd_del



curl http://************:7011/dc_service_a/esop/ads_th_invmbnk_project_dd_del



http://************:7011/dc_service_a/user_center/dws_par_employee_withbroker_dd_del


curl http://************:7011/dc_service_a/ct_ods_hivedc/dim_metadata_data_system_info_del

curl http://************:7011/dc_service/performance/ADS_CB_COOP_CLIENT_FIN_PRF_YD

curl http://************:7011/data-service/performance/TABLE_JOB_STATUS_NEW2

curl http://************:7011/dc_service_a/performance/ADS_EVT_SLO_PROFIT_DD


curl http://************:7011/dc_service_a/szth/ads_organcust_base_info_dd_clear

curl http://************:7011/dc_service_a/dops/dim_metadata_data_system_info_clear

curl http://************:7011/dc_service_a/performance/ads_cm_branch_emp_stats_dd


curl http://************:7011/dc_service_a/performance/AGG_BRANCH_STAFF_DI


curl http://************:7011/dc_service_a/ums/dwd_par_client_mobile_relation_dd_clear


curl http://************:7011/dc_service_a/zlzh/ads_organcust_city_branch_director_dd_clear


curl http://************:7011/dc_service_a/dc_service_a/ndc/record_log_del

curl http://************:7011/dc_service_a/zlzh/ads_th_ipocomprfa_dtl_dd_clear


curl http://************:7011/dc_service_a/performance/ads_th_bond_serv_trad_dd_clear


http://************:7011/dc_service_a/performance/ads_cm_scsm_exch_trd_stats_di

curl http://************:7011/dc_service_a/performance/ads_th_bond_serv_trad_dd
curl http://************:7011/data-service/performance/TABLE_JOB_STATUS_NEW8

http://************:7011/dc_service_a/performance/ads_pw_agg_clerk_secutrdbal_dd

http://************:7011/dc_service_a/performance/agg_branch_secutrdbal_dd


curl http://************:7011/dc_service_a/sysprepar/hiveods

curl http://************:7011/dc_service_a/account_book/ads_organcust_ent_uf20_detail_di_del

curl http://************:7011/dc_service_a/performance/ads_cb_branch_crdt_stat_di

curl http://************:7011/dc_service_a/performance/AGG_CLIENTS_BRANCH_INC_Y_MI



curl http://************:7011/dc_service_a/ums/dwd_par_client_mobile_relation_dd_del

curl http://************:7011/dc_service_a/performance/fundaccount_daily


curl http://************:7011/dc_service_a/organization_app/ads_organcust_base_info_dd_clear


curl http://************:7011/dc_service_a/fkjs/tsys_task_info_unif

curl http://************:7011/dc_service_a/TGZL/ASHAREPLACEMENTDETAILS_DEL


curl http://************:7011/dc_service_a/performance/agg_branch_rating_di

curl http://************:7011/dc_service_a/performance/ads_th_bond_serv_trad_dd



curl http://************:7011/dc_service_a/investbank/ads_project_contract_info_del
curl http://************:7011/dc_service_a/investbank/ads_project_contractor_info_del
curl http://************:7011/dc_service_a/investbank/ads_project_reserve_info_del

curl http://************:7011/dc_service_a/esop-wealth/ads_th_project_person_dd_del
curl http://************:7011/dc_service_a/investbank/ads_life_cycle_detail_del


curl http://************:7011/dc_service_a/investbank/ads_bonds_details_del
curl http://************:7011/dc_service_a/investbank/ads_stock_details_del

http://************:7011/xyl-dc-dataquality/SYSPREPAR/detail/tipcheck?systemName=UF20&status=1

curl http://************:7011/dc_service_a/performance/ads_evt_ina_client_incm_dtl_mi


http://************:7011/dc_service_a/szth/ads_project_base_info_del
http://************:7011/dc_service_a/szth/ads_project_base_extend_info_del
http://************:7011/dc_service_a/szth/ads_project_bond_list_del
http://************:7011/dc_service_a/szth/ads_project_bond_listing_place_del

curl http://************:7011/dc_service_a/aml/task_management_1
curl http://************:7011/dc_service_a/aml/task_management_2

curl http://************:7011/dc_service_a/ndc/cdd_rela_cust_entr_jour_del
curl http://************:7011/dc_service_a/ndc/cdd_rela_cust_logn_log_del
curl http://************:7011/dc_service_a/ndc/cdd_rela_cust_scr_fm_entr_del


curl http://************:7011/dc_service_a/performance/dws_inf_market_trade_di 


curl http://************:7011/dc_service_a/esop-wealth/ads_th_iss_bond_detail_di_del

curl http://************:7011/dc_service_a/performance/ads_th_strategy_system_dtl_dd

curl http://************:7011/dc_service_a/zhfx/dm_acct_in
curl http://************:7011/dc_service_a/zhfx/dm_acct_ga

curl http://************:7011/dc_service_a/jy/mt_tradingstat

curl http://************:7011/dc_service_a/wind/asharemargintradesum



curl http://************:7011/dc_service_b/performance/ADVISERSTG

curl http://************:7011/dc_service_b/performance/DWS_AST_COMBADV_ASSETFARE_DI


curl 'http://10.30.50.221:9201/dops/api/execute/v1/ndc/sysprepar_status_update?accessToken=90307b3b3cad46729b14c453008a6af73bdd0c2ba24a4697d1e0e4e4502c6fce&system_name=CFZY_PUSH_0530'

curl -X GET -G --data-urlencode 'accessToken=90307b3b3cad46729b14c453008a6af73bdd0c2ba24a4697d1e0e4e4502c6fce' http://10.30.50.221:9201/dops/api/execute/v1/ctzgdcnt/sysprepar 

curl 'http://10.30.50.221:9201/dops/api/execute/v1/ctzgdcnt/sysprepar?accessToken=90307b3b3cad46729b14c453008a6af73bdd0c2ba24a4697d1e0e4e4502c6fce'

select count(1) from table_job_status t where t.table_name = &表名 and &日期 between t.busi_sdate and t.busi_edate and status = 1

SELECT COUNT(1) AS CNT FROM PERFORMANCE.TABLE_JOB_STATUS T WHERE T.TABLE_NAME IN ('AGG_BRANCH_RATING_DI') AND to_char(sysdate-1,'yyyyMMdd') BETWEEN T.BUSI_SDATE AND T.BUSI_EDATE AND STATUS = 1

DELETE a
FROM strategic_command.ads_organcust_city_branch_director_dd AS a
JOIN
(
    SELECT  MAX(update_time) AS max_update_time
    FROM strategic_command.ads_organcust_city_branch_director_dd
) AS b
ON a.update_time < b.max_update_time;


DELETE FROM PERFORMANCE.ADS_TH_BOND_SERV_TRAD_DD
WHERE UPDATETIME < (
SELECT MAX(UPDATETIME)
FROM PERFORMANCE.ADS_TH_BOND_SERV_TRAD_DD
);

INSERT INTO TABLE_JOB_STATUS (OWNER, TABLE_NAME, TABLE_COMMENT, TABLE_CATEGORY, STATUS, BUSI_SDATE, BUSI_EDATE, STIME, ETIME, REMARK, DATA_SOURCES)
VALUES ('PERFORMANCE', 'ADS_TH_BOND_SERV_TRAD_DD', '债券服务商明细', 0, 1, &数据开始日期, &数据结束日期, &推送开始时间, &推送结束时间, null, 1);

INSERT INTO TABLE_JOB_STATUS (OWNER, TABLE_NAME, TABLE_COMMENT, TABLE_CATEGORY, STATUS, BUSI_SDATE, BUSI_EDATE, STIME, ETIME, REMARK, DATA_SOURCES)
VALUES ('PERFORMANCE', 'ADS_PW_AGG_CLERK_SECUTRDBAL_DD', '人员考核证券交易量', 0, 1, &数据开始日期, &数据结束日期, &推送开始时间, &推送结束时间, null, 1);


INSERT INTO PERFORMANCE.TABLE_JOB_STATUS select DISTINCT 'PERFORMANCE', 'AGG_BRANCH_SECUTRDBAL_DD', '分支机构考核证券交易量', 0, 1, busi_date, busi_date, SYSDATE, SYSDATE, null, 1 from PERFORMANCE.AGG_BRANCH_SECUTRDBAL_DD where updatetime >= To_date(To_char(Trunc(SYSDATE), 'yyyy/mm/dd hh24:mi:ss'), 'yyyy/mm/dd hh24:mi:ss')

/performance/agg_branch_secutrdbal_dd


INSERT INTO TABLE_JOB_STATUS (OWNER, TABLE_NAME, TABLE_COMMENT, TABLE_CATEGORY, STATUS, BUSI_SDATE, BUSI_EDATE, STIME, ETIME, REMARK, DATA_SOURCES)
VALUES ('PERFORMANCE', 'AGG_BRANCH_SECUTRDBAL_DD', '分支机构考核证券交易量', 0, 1, &数据开始日期, &数据结束日期, &推送开始时间, &推送结束时间, null, 1);



INSERT INTO TABLE_JOB_STATUS (OWNER, TABLE_NAME, TABLE_COMMENT, TABLE_CATEGORY, STATUS, BUSI_SDATE, BUSI_EDATE, STIME, ETIME, REMARK, DATA_SOURCES)
VALUES ('PERFORMANCE', 'ADS_CB_BRANCH_CRDT_STAT_DI', '营业部融资融券汇总新表', 0, 1, &数据开始日期, &数据结束日期, &推送开始时间, &推送结束时间, null, 1);


INSERT INTO PERFORMANCE.TABLE_JOB_STATUS select DISTINCT 'PERFORMANCE', 'ADS_CB_BRANCH_CRDT_STAT_DI', '营业部融资融券汇总新表', 0, 1, INIT_DATE, INIT_DATE , SYSDATE, SYSDATE, null, 1 from PERFORMANCE.ADS_CB_BRANCH_CRDT_STAT_DI where updatetime >= To_date(To_char(Trunc(SYSDATE), 'yyyy/mm/dd hh24:mi:ss'), 'yyyy/mm/dd hh24:mi:ss')

INSERT INTO TABLE_JOB_STATUS (OWNER, TABLE_NAME, TABLE_COMMENT, TABLE_CATEGORY, STATUS, BUSI_SDATE, BUSI_EDATE, STIME, ETIME, REMARK, DATA_SOURCES)
VALUES ('PERFORMANCE', 'FUNDACCOUNT_DAILY', '资产账户表(快照表)', 0, 1, &数据开始日期, &数据结束日期, &推送开始时间, &推送结束时间, null, 1);



INSERT INTO PERFORMANCE.TABLE_JOB_STATUS select DISTINCT 'PERFORMANCE', 'AGG_BRANCH_RATING_DI', '分支机构分类评定指标日表', 0, 1, busi_date, busi_date, SYSDATE, SYSDATE, null, 1 from PERFORMANCE.FUNDACCOUNT_DAILY where updatetime >= To_date(To_char(Trunc(SYSDATE), 'yyyy/mm/dd hh24:mi:ss'), 'yyyy/mm/dd hh24:mi:ss')


INSERT INTO TABLE_JOB_STATUS (OWNER, TABLE_NAME, TABLE_COMMENT, TABLE_CATEGORY, STATUS, BUSI_SDATE, BUSI_EDATE, STIME, ETIME, REMARK, DATA_SOURCES)
VALUES ('PERFORMANCE', 'ADS_EVT_INA_CLIENT_INCM_DTL_MI', '投顾客户订单创收', 0, 1, &数据开始日期, &数据结束日期, &推送开始时间, &推送结束时间, null, 1);


INSERT INTO PERFORMANCE.TABLE_JOB_STATUS select DISTINCT 'PERFORMANCE', 'ADS_EVT_INA_CLIENT_INCM_DTL_MI', '投顾客户订单创收', 0, 1, busi_date, busi_date, SYSDATE, SYSDATE, null, 1 from PERFORMANCE.ADS_EVT_INA_CLIENT_INCM_DTL_MI where updatetime >= To_date(To_char(Trunc(SYSDATE), 'yyyy/mm/dd hh24:mi:ss'), 'yyyy/mm/dd hh24:mi:ss')


INSERT INTO TABLE_JOB_STATUS (OWNER, TABLE_NAME, TABLE_COMMENT, TABLE_CATEGORY, STATUS, BUSI_SDATE, BUSI_EDATE, STIME, ETIME, REMARK, DATA_SOURCES)
VALUES ('PERFORMANCE', 'DWS_INF_MARKET_TRADE_DI', '证券交易量数据表', 0, 1, &数据开始日期, &数据结束日期, &推送开始时间, &推送结束时间, null, 1);

INSERT INTO PERFORMANCE.TABLE_JOB_STATUS select DISTINCT 'PERFORMANCE', 'DWS_INF_MARKET_TRADE_DI', '证券交易量数据表', 0, 1, to_char(sysdate-1,'yyyyMMdd'), to_char(sysdate-1,'yyyyMMdd'), SYSDATE, SYSDATE, null, 1 from PERFORMANCE.DWS_INF_MARKET_TRADE_DI where updatetime >= To_date(To_char(Trunc(SYSDATE), 'yyyy/mm/dd hh24:mi:ss'), 'yyyy/mm/dd hh24:mi:ss')


INSERT INTO TABLE_JOB_STATUS (OWNER, TABLE_NAME, TABLE_COMMENT, TABLE_CATEGORY, STATUS, BUSI_SDATE, BUSI_EDATE, STIME, ETIME, REMARK, DATA_SOURCES)
VALUES ('PERFORMANCE', 'ADS_TH_STRATEGY_SYSTEM_DTL_DD', '战略指挥系统投行指标明细', 0, 1, &数据开始日期, &数据结束日期, &推送开始时间, &推送结束时间, null, 1);

INSERT INTO PERFORMANCE.TABLE_JOB_STATUS select DISTINCT 'PERFORMANCE', 'ADS_TH_STRATEGY_SYSTEM_DTL_DD', '战略指挥系统投行指标明细', 0, 1, busi_date, busi_date, SYSDATE, SYSDATE, null, 1 from PERFORMANCE.ADS_TH_STRATEGY_SYSTEM_DTL_DD where updatetime >= To_date(To_char(Trunc(SYSDATE), 'yyyy/mm/dd hh24:mi:ss'), 'yyyy/mm/dd hh24:mi:ss')


INSERT INTO TABLE_JOB_STATUS (OWNER, TABLE_NAME, TABLE_COMMENT, TABLE_CATEGORY, STATUS, BUSI_SDATE, BUSI_EDATE, STIME, ETIME, REMARK, DATA_SOURCES)
VALUES ('PERFORMANCE', 'ADVISERSTG', '投顾策略表', 0, 1, &数据开始日期, &数据结束日期, &推送开始时间, &推送结束时间, null, 1);

INSERT INTO PERFORMANCE.TABLE_JOB_STATUS select DISTINCT 'PERFORMANCE', 'ADVISERSTG', '投顾策略表', 0, 1, to_char(sysdate,'yyyyMMdd'), to_char(sysdate,'yyyyMMdd'), SYSDATE, SYSDATE, null, 1 from PERFORMANCE.ADVISERSTG where updatetime >= To_date(To_char(Trunc(SYSDATE), 'yyyy/mm/dd hh24:mi:ss'), 'yyyy/mm/dd hh24:mi:ss')


INSERT INTO TABLE_JOB_STATUS (OWNER, TABLE_NAME, TABLE_COMMENT, TABLE_CATEGORY, STATUS, BUSI_SDATE, BUSI_EDATE, STIME, ETIME, REMARK, DATA_SOURCES)
VALUES ('PERFORMANCE', 'DWS_AST_COMBADV_ASSETFARE_DI', '基金投顾资产费用表', 0, 1, &数据开始日期, &数据结束日期, &推送开始时间, &推送结束时间, null, 1);

INSERT INTO PERFORMANCE.TABLE_JOB_STATUS select DISTINCT 'PERFORMANCE', 'DWS_AST_COMBADV_ASSETFARE_DI', '基金投顾资产费用表', 0, 1, busi_date, busi_date, SYSDATE, SYSDATE, null, 1 from PERFORMANCE.DWS_AST_COMBADV_ASSETFARE_DI where updatetime >= To_date(To_char(Trunc(SYSDATE), 'yyyy/mm/dd hh24:mi:ss'), 'yyyy/mm/dd hh24:mi:ss')

INSERT INTO TABLE_JOB_STATUS (OWNER, TABLE_NAME, TABLE_COMMENT, TABLE_CATEGORY, STATUS, BUSI_SDATE, BUSI_EDATE, STIME, ETIME, REMARK, DATA_SOURCES)
VALUES ('PERFORMANCE', 'ADS_CM_DIAGNOSIS_BRANCH_MI', '分支机构一分一诊表', 0, 1, &数据开始日期, &数据结束日期, &推送开始时间, &推送结束时间, null, 1);

INSERT INTO PERFORMANCE.TABLE_JOB_STATUS select DISTINCT 'PERFORMANCE', 'ADS_CM_DIAGNOSIS_BRANCH_MI', '分支机构一分一诊表', 0, 1, to_char(sysdate-1,'yyyyMMdd'), to_char(sysdate-1,'yyyyMMdd'), SYSDATE, SYSDATE, null, 1 from PERFORMANCE.ADS_CM_DIAGNOSIS_BRANCH_MI where updatetime >= To_date(To_char(Trunc(SYSDATE), 'yyyy/mm/dd hh24:mi:ss'), 'yyyy/mm/dd hh24:mi:ss')

INSERT INTO TABLE_JOB_STATUS (OWNER, TABLE_NAME, TABLE_COMMENT, TABLE_CATEGORY, STATUS, BUSI_SDATE, BUSI_EDATE, STIME, ETIME, REMARK, DATA_SOURCES)
VALUES ('PERFORMANCE', 'AGG_BRANCH_REST_STKPOSI_DI', '分支机构限售持仓表', 0, 1, &数据开始日期, &数据结束日期, &推送开始时间, &推送结束时间, null, 1);

INSERT INTO PERFORMANCE.TABLE_JOB_STATUS select DISTINCT 'PERFORMANCE', 'AGG_BRANCH_REST_STKPOSI_DI', '分支机构限售持仓表', 0, 1, busi_date, busi_date, SYSDATE, SYSDATE, null, 1 from PERFORMANCE.AGG_BRANCH_REST_STKPOSI_DI where updatetime >= To_date(To_char(Trunc(SYSDATE), 'yyyy/mm/dd hh24:mi:ss'), 'yyyy/mm/dd hh24:mi:ss')


INSERT INTO TABLE_JOB_STATUS (OWNER, TABLE_NAME, TABLE_COMMENT, TABLE_CATEGORY, STATUS, BUSI_SDATE, BUSI_EDATE, STIME, ETIME, REMARK, DATA_SOURCES)
VALUES ('PERFORMANCE', 'AGG_BRANCH_ORG_CLIENT_INC_MI', '分支机构机构客户创收表', 0, 1, &数据开始日期, &数据结束日期, &推送开始时间, &推送结束时间, null, 1);

INSERT INTO PERFORMANCE.TABLE_JOB_STATUS select DISTINCT 'PERFORMANCE', 'AGG_BRANCH_ORG_CLIENT_INC_MI', '分支机构机构客户创收表', 0, 1, BUSI_MONTH, BUSI_MONTH, SYSDATE, SYSDATE, null, 1 from PERFORMANCE.AGG_BRANCH_ORG_CLIENT_INC_MI where updatetime >= To_date(To_char(Trunc(SYSDATE), 'yyyy/mm/dd hh24:mi:ss'), 'yyyy/mm/dd hh24:mi:ss')


INSERT INTO TABLE_JOB_STATUS (OWNER, TABLE_NAME, TABLE_COMMENT, TABLE_CATEGORY, STATUS, BUSI_SDATE, BUSI_EDATE, STIME, ETIME, REMARK, DATA_SOURCES)
VALUES ('PERFORMANCE', 'ADS_EVT_PROD_SELL_INFO_DI', '产品销售日粒度表', 0, 1, &数据开始日期, &数据结束日期, &推送开始时间, &推送结束时间, null, 1);


INSERT INTO PERFORMANCE.TABLE_JOB_STATUS select DISTINCT 'PERFORMANCE', 'ADS_EVT_PROD_SELL_INFO_DI', '产品销售日粒度表', 0, 1, BUSI_DATE, BUSI_DATE, SYSDATE, SYSDATE, null, 1 from PERFORMANCE.ADS_EVT_PROD_SELL_INFO_DI where updatetime >= To_date(To_char(Trunc(SYSDATE), 'yyyy/mm/dd hh24:mi:ss'), 'yyyy/mm/dd hh24:mi:ss')

INSERT INTO TABLE_JOB_STATUS (OWNER, TABLE_NAME, TABLE_COMMENT, TABLE_CATEGORY, STATUS, BUSI_SDATE, BUSI_EDATE, STIME, ETIME, REMARK, DATA_SOURCES)
VALUES ('PERFORMANCE', 'POFA_COMB_CONVERT_DISCOUNT', '基金投顾表', 0, 1, &数据开始日期, &数据结束日期, &推送开始时间, &推送结束时间, null, 1);

INSERT INTO PERFORMANCE.TABLE_JOB_STATUS select DISTINCT 'PERFORMANCE', 'POFA_COMB_CONVERT_DISCOUNT', '基金投顾表', 0, 1, BUSI_DATE, BUSI_DATE, SYSDATE, SYSDATE, null, 1 from PERFORMANCE.POFA_COMB_CONVERT_DISCOUNT where updatetime >= To_date(To_char(Trunc(SYSDATE), 'yyyy/mm/dd hh24:mi:ss'), 'yyyy/mm/dd hh24:mi:ss')

INSERT INTO TABLE_JOB_STATUS (OWNER, TABLE_NAME, TABLE_COMMENT, TABLE_CATEGORY, STATUS, BUSI_SDATE, BUSI_EDATE, STIME, ETIME, REMARK, DATA_SOURCES)
VALUES ('PERFORMANCE', 'ADS_PW_AGG_BRANCH_PLEDGE_MD', '股票质押分支机构月表', 0, 1, &数据开始日期, &数据结束日期, &推送开始时间, &推送结束时间, null, 1);

INSERT INTO PERFORMANCE.TABLE_JOB_STATUS select DISTINCT 'PERFORMANCE', 'ADS_PW_BRANCH_FINANCE_INDEX_MI', '分支机构考核利润月表', 0, 1, BUSI_MONTH, BUSI_MONTH, SYSDATE, SYSDATE, null, 1 from PERFORMANCE.ADS_PW_BRANCH_FINANCE_INDEX_MI where updatetime >= To_date(To_char(Trunc(SYSDATE), 'yyyy/mm/dd hh24:mi:ss'), 'yyyy/mm/dd hh24:mi:ss')

curl -X GET -G --data-urlencode 'accessToken=90307b3b3cad46729b14c453008a6af73bdd0c2ba24a4697d1e0e4e4502c6fce' http://10.30.50.221:9201/dops/api/execute/v1/smta/sysprepar

curl 'http://10.30.50.221:9201/dops/api/execute/v1/smta/sysprepar?accessToken=90307b3b3cad46729b14c453008a6af73bdd0c2ba24a4697d1e0e4e4502c6fce'
curl 'http://10.30.50.221:9201/dops/api/execute/v1/gmta/sysprepar?accessToken=90307b3b3cad46729b14c453008a6af73bdd0c2ba24a4697d1e0e4e4502c6fce'
curl 'http://10.30.50.221:9201/dops/api/execute/v1/subta/sysprepar?accessToken=90307b3b3cad46729b14c453008a6af73bdd0c2ba24a4697d1e0e4e4502c6fce'


curl -X GET -G --data-urlencode 'accessToken=90307b3b3cad46729b14c453008a6af73bdd0c2ba24a4697d1e0e4e4502c6fce' http://10.30.50.221:9201/dops/api/execute/v1/crm/t_proc_log


http://10.30.50.221:9201/dops/api/execute/v1/crm/t_proc_log?accessToken=90307b3b3cad46729b14c453008a6af73bdd0c2ba24a4697d1e0e4e4502c6fce&proc_no=709211





http://10.30.50.221:9201/dops/api/execute/v1/crm/ads_clerk_prod_sell_di?accessToken=90307b3b3cad46729b14c453008a6af73bdd0c2ba24a4697d1e0e4e4502c6fce

http://10.30.50.221:9201/dops/api/execute/v1/performance/agg_branch_trade_prod?accessToken=90307b3b3cad46729b14c453008a6af73bdd0c2ba24a4697d1e0e4e4502c6fce

http://10.30.50.221:9201/dops/api/execute/v1/performance/agg_clerk_trade_prod?accessToken=90307b3b3cad46729b14c453008a6af73bdd0c2ba24a4697d1e0e4e4502c6fce

http://10.30.50.221:9201/dops/api/execute/v1/performance/agg_branch_client_base?accessToken=90307b3b3cad46729b14c453008a6af73bdd0c2ba24a4697d1e0e4e4502c6fce

http://10.30.50.221:9201/dops/api/execute/v1/performance/agg_clerk_client_base?accessToken=90307b3b3cad46729b14c453008a6af73bdd0c2ba24a4697d1e0e4e4502c6fce



http://10.30.50.221:9201/dops/api/execute/v1/performance/agg_branch_crdt?accessToken=90307b3b3cad46729b14c453008a6af73bdd0c2ba24a4697d1e0e4e4502c6fce





http://10.30.50.221:9201/dops/api/execute/v1/jrysp/zy_fund_detial?accessToken=90307b3b3cad46729b14c453008a6af73bdd0c2ba24a4697d1e0e4e4502c6fce




http://10.30.50.221:9201/dops/api/execute/v1/SJJS_UNIF?accessToken=90307b3b3cad46729b14c453008a6af73bdd0c2ba24a4697d1e0e4e4502c6fce

http://10.30.50.221:9201/dops/api/execute/v1/jy/mt_tradingstat?accessToken=90307b3b3cad46729b14c453008a6af73bdd0c2ba24a4697d1e0e4e4502c6fce


http://10.30.50.221:9201/dops/api/execute/v1/aml/task_management_1?accessToken=90307b3b3cad46729b14c453008a6af73bdd0c2ba24a4697d1e0e4e4502c6fce



http://10.30.50.221:9201/dops/api/execute/v1/performance/agg_clerk_crdt?accessToken=90307b3b3cad46729b14c453008a6af73bdd0c2ba24a4697d1e0e4e4502c6fce



http://10.30.50.221:9201/dops/api/execute/sysprepar/hiveods?accessToken=90307b3b3cad46729b14c453008a6af73bdd0c2ba24a4697d1e0e4e4502c6fce



http://10.30.50.221:9201/dops/api/execute/sysprepar/hiveods?accessToken=90307b3b3cad46729b14c453008a6af73bdd0c2ba24a4697d1e0e4e4502c6fce

http://10.30.50.221:9201/dops/api/execute/v1/ndc_sysprepar?accessToken=90307b3b3cad46729b14c453008a6af73bdd0c2ba24a4697d1e0e4e4502c6fce&systemname=DS_GMTG_ITS
http://10.30.50.221:9201/dops/api/execute/v1/ndc_sysprepar?accessToken=90307b3b3cad46729b14c453008a6af73bdd0c2ba24a4697d1e0e4e4502c6fce&systemname=UF20


http://10.30.50.221:9201/dops/api/execute/v1/ndc/sysprepar_update_4?accessToken=90307b3b3cad46729b14c453008a6af73bdd0c2ba24a4697d1e0e4e4502c6fce&system_name=ZJGMTA

http://10.30.50.221:9201/dops/api/execute/v1/ndc/sysprepar_update_4?accessToken=90307b3b3cad46729b14c453008a6af73bdd0c2ba24a4697d1e0e4e4502c6fce&system_name=ODS_DC_2
"Affected rows":1


"status":"success"

curl 'http://************:7011/dc_service_o/fxq/t_rst_iis2?page=1&page_size=100&busi_date=20241125'
http://10.30.50.221:9201/dops/api/execute/v1/performance/agg_branch_client_trd_base?accesstoken=90307b3b3cad46729b14c453008a6af73bdd0c2ba24a4697d1e0e4e4502c6fce
http://10.30.50.221:9201/dops/api/execute/v1/performance/agg_branch_client_trd_base?accessToken=90307b3b3cad46729b14c453008a6af73bdd0c2ba24a4697d1e0e4e4502c6fce
http://10.30.50.221:9201/dops/api/execute/v1/performance/agg_clerk_client_trd_base?accessToken=90307b3b3cad46729b14c453008a6af73bdd0c2ba24a4697d1e0e4e4502c6fce


curl 'http://10.30.50.221:9201/dops/api/execute/fxq/t_rst_iis?accessToken=90307b3b3cad46729b14c453008a6af73bdd0c2ba24a4697d1e0e4e4502c6fce'

http://10.30.50.221:9201/dops/api/execute/v1/crm/t_proc_log?accessToken=90307b3b3cad46729b14c453008a6af73bdd0c2ba24a4697d1e0e4e4502c6fce&proc_no=709211


http://10.30.50.221:9201/dops/api/execute/v1/performance/agg_clerk_client_base?accessToken=90307b3b3cad46729b14c453008a6af73bdd0c2ba24a4697d1e0e4e4502c6fce


http://10.30.50.221:9201/dops/api/execute/uf20/businoperlog_rt?accessToken=48949e02200cdadb52c2bb5ca9c680258100771205c68f9cd7bdacd070c5fcab



curl -G 'http://10.30.50.221:9201/dops/api/execute/oracle/performance/table_job/status' \
  --data-urlencode "accessToken=48949e02200cdadb52c2bb5ca9c680258100771205c68f9cd7bdacd070c5fcab" \
  --data-urlencode "table_name='AGG_BRANCH_PROD_MTK_VALUE_DI','AGG_BRANCH_PROD_MTK_VALUE_WI','AGG_BRANCH_PROD_MTK_VALUE_MI','AGG_BRANCH_PROD_MTK_VALUE_QI','AGG_BRANCH_PROD_MTK_VALUE_YI'"







