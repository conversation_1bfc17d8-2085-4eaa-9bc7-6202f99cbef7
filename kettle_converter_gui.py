import tkinter as tk
from tkinter import scrolledtext
import re

def convert_kettle_command(command):
    """
    Converts a single Kettle command line to the simplified kettle_start.sh format.
    """
    command = command.strip()
    
    # Case 1: sudo su - hive -c '...'
    if command.startswith('sudo su - hive -c'):
        inner_command_match = re.search(r"'(.*)'", command)
        if not inner_command_match:
            return f"# UNABLE TO PARSE: {command}"

        inner_command = inner_command_match.group(1)
        
        task_type = ''
        if 'kitchen.sh' in inner_command:
            task_type = 'job'
            dir_param = '-dir'
            task_param = '-job'
        elif 'pan.sh' in inner_command:
            task_type = 'trans'
            dir_param = '-dir'
            task_param = '-trans'
        else:
            return f"# UNRECOGNIZED SCRIPT IN: {command}"

        dir_match = re.search(fr'{dir_param}\s+([/\w_.-]+)', inner_command)
        task_match = re.search(fr'{task_param}\s+([/\w_.-]+)', inner_command)

        if dir_match and task_match:
            dir_path = dir_match.group(1)
            task_name = task_match.group(1)
            return f"sh ETL/scripts/kettle_start.sh {task_type} '{dir_path}|{task_name}'"
        else:
            return f"# UNABLE TO EXTRACT PARAMS FROM: {command}"

    # Case 2: sh /opt/script/kettle/kettle_start.sh ...
    elif command.startswith('sh /opt/script/kettle/kettle_start.sh'):
        parts = command.split()
        if len(parts) >= 4:
            task_type = parts[2]
            path_and_task = " ".join(parts[3:])
            return f"sh ETL/scripts/kettle_start.sh {task_type} {path_and_task}"
        else:
            return f"# UNABLE TO PARSE: {command}"
            
    # If the command is already in the desired format or doesn't match, return it as is
    else:
        return command

class KettleConverterApp:
    def __init__(self, root):
        self.root = root
        self.root.title("Kettle命令转换工具")
        self.root.geometry("700x500")

        # Input Frame
        input_frame = tk.Frame(root, padx=10, pady=5)
        input_frame.pack(fill="x")
        tk.Label(input_frame, text="输入原始命令:").pack(anchor="w")
        self.input_text = scrolledtext.ScrolledText(root, height=10, width=80)
        self.input_text.pack(padx=10, pady=5, fill="both", expand=True)

        # Button Frame
        button_frame = tk.Frame(root, pady=10)
        button_frame.pack()
        tk.Button(button_frame, text="转换", command=self.convert_commands, width=10).pack(side="left", padx=5)
        tk.Button(button_frame, text="复制", command=self.copy_to_clipboard, width=10).pack(side="left", padx=5)
        tk.Button(button_frame, text="清空", command=self.clear_text, width=10).pack(side="left", padx=5)

        # Output Frame
        output_frame = tk.Frame(root, padx=10, pady=5)
        output_frame.pack(fill="x")
        tk.Label(output_frame, text="转换后的命令:").pack(anchor="w")
        self.output_text = scrolledtext.ScrolledText(root, height=10, width=80)
        self.output_text.pack(padx=10, pady=5, fill="both", expand=True)

    def convert_commands(self):
        input_commands = self.input_text.get("1.0", tk.END).strip().split('\n')
        converted_commands = []
        for command in input_commands:
            if command.strip():
                converted_commands.append(convert_kettle_command(command))
        
        self.output_text.delete("1.0", tk.END)
        self.output_text.insert(tk.END, "\n".join(converted_commands))

    def copy_to_clipboard(self):
        self.root.clipboard_clear()
        self.root.clipboard_append(self.output_text.get("1.0", tk.END))

    def clear_text(self):
        self.input_text.delete("1.0", tk.END)
        self.output_text.delete("1.0", tk.END)

if __name__ == "__main__":
    root = tk.Tk()
    app = KettleConverterApp(root)
    root.mainloop()
