export LD_LIBRARY_PATH=$LD_LIBRARY_PATH:/usr/lib/oracle/21/client64/lib

source ~/.bashrc


hive -e "SELECT * FROM dm_acctanal.ads_prod_profit_di where busi_date >= ******** " > /var/lib/hive/tmp/stg_data/hive/ads_prod_profit_di.dat



/usr/lib/oracle/21/client64/bin/sqlldr CT_ODS_DC/CT_ODS_DC@10.30.98.81:1521/dcodsdb control=/var/lib/hive/tmp/stg_data/hive/ads_prod_profit_di.tcl log=/var/lib/hive/tmp/stg_data/hive/ads_prod_profit_di_t.log 


/usr/lib/oracle/21/client64/bin/sqlldr CT_ODS_DC/CT_ODS_DC@10.30.98.81:1521/dcodsdb control=/var/lib/hive/tmp/stg_data/hive/ads_prod_profit_di_2.tcl log=/var/lib/hive/tmp/stg_data/hive/ads_prod_profit_di_t_2.log ERRORS=0 ROWS=100000 PARALLEL=8 MULTITHREADING=true
 

/usr/lib/oracle/21/client64/bin/sqlldr CT_ODS_DC/CT_ODS_DC@10.30.98.81:1521/dcodsdb control=/var/lib/hive/tmp/stg_data/hive/ads_prod_profit_di_2.tcl log=/var/lib/hive/tmp/stg_data/hive/ads_prod_profit_di_t_2.log ERRORS=0 rows=100000 readsize=20971520 bindsize=20971520 PARALLEL=TRUE




/usr/lib/oracle/21/client64/bin/sqlldr CT_ODS_DC/CT_ODS_DC@10.30.98.81:1521/dcodsdb control=/var/lib/hive/tmp/stg_data/hive/ads_prod_profit_di_2.tcl log=/var/lib/hive/tmp/stg_data/hive/ads_prod_profit_di_t_2.log


#补数操作
kinit -kt ./ETL/keytab/hive.keytab hive
hive -e "SELECT * FROM dm_acctanal.ads_prod_profit_di where busi_date <= 20230428"  |  grep -v "WARN" > /data01/sqlldr/stg_data/ads_prod_profit_di/ads_prod_profit_di.dat

/usr/lib/oracle/21/client64/bin/sqlldr SJZX/'ctzqxy262700!'@10.30.98.111:1521/appdsjdb control=/data01/sqlldr/stg_data/ads_prod_profit_di/ads_prod_profit_di.tcl log=/data01/sqlldr/stg_data/ads_prod_profit_di/ads_prod_profit_di.log 

#数据中心测试
/usr/lib/oracle/21/client64/bin/sqlldr CT_ODS_DC/CT_ODS_DC@10.30.98.81:1521/dcodsdb control=/data01/sqlldr/stg_data/ads_prod_profit_di/ads_prod_profit_di_test.tcl log=/data01/sqlldr/stg_data/ads_prod_profit_di/ads_prod_profit_di_test.log 


------------------------------------------------------------------------------------------------------------------------------------------------------

1. 账户分析-场内-ADS_STOCK_HOLDING_PROFIT_DI
## hive表数据导出
hive -e "set hive.exec.compress.output=true;
set hive.exec.parallel=true;
set hive.exec.max.dynamic.partitions=60000;
set hive.exec.max.dynamic.partitions.pernode=60000;
set mapreduce.map.memory.mb=65536;
set mapreduce.reduce.memory.mb=65536;
set mapreduce.map.java.opts=-Xmx32768m;
SELECT * FROM dm_acctanal.ads_stock_holding_profit_di where busi_date >= ******** and busi_date < ********"  |  grep -v "WARN" > /data01/sqlldr/stg_data/ads_stock_holding_profit_di/ads_stock_holding_profit_di.dat


/usr/lib/oracle/21/client64/bin/sqlldr SJZX/'ctzqxy262700!'@10.30.98.133:1521/dsjfxdb control=/data01/sqlldr/stg_data/ads_stock_holding_profit_di/ads_stock_holding_profit_di.tcl log=/data01/sqlldr/stg_data/ads_stock_holding_profit_di/ads_stock_holding_profit_di_2022.log 


## 测试数据
hive -e "SELECT * FROM dm_acctanal.ads_stock_holding_profit_di where busi_date = ********"  |  grep -v "WARN" > /data01/sqlldr/stg_data/ads_stock_holding_profit_di/ads_stock_holding_profit_di_test.dat

------------------------------------------------------------------------------------------------------------------------------------------------------

2.ADS_STOCK_HOLD_ACCOUNT_DI

## hive表数据导出
hive -e "SELECT * FROM dm_acctanal.ads_stock_hold_account_di where busi_date >= ********"  |  grep -v "WARN" > /data01/sqlldr/stg_data/ads_stock_hold_account_di/ads_stock_hold_account_di.dat


/usr/lib/oracle/21/client64/bin/sqlldr SJZX/'ctzqxy262700!'@10.30.98.133:1521/dsjfxdb control=/data01/sqlldr/stg_data/ads_stock_hold_account_di/ads_stock_hold_account_di.tcl log=/data01/sqlldr/stg_data/ads_stock_hold_account_di/ads_stock_hold_account_di.log 


## 测试数据
hive -e "SELECT * FROM dm_acctanal.ads_stock_hold_account_di where busi_date = ********"  |  grep -v "WARN" > /data01/sqlldr/stg_data/ads_stock_hold_account_di/ads_stock_hold_account_di_test.dat

------------------------------------------------------------------------------------------------------------------------------------------------------


3.ads_stock_accm_account_di
## hive表数据导出
hive -e "SELECT * FROM dm_acctanal.ads_stock_accm_account_di where busi_date >= ********"  |  grep -v "WARN" > /data01/sqlldr/stg_data/ads_stock_accm_account_di/ads_stock_accm_account_di.dat


/usr/lib/oracle/21/client64/bin/sqlldr SJZX/'ctzqxy262700!'@10.30.98.133:1521/dsjfxdb control=/data01/sqlldr/stg_data/ads_stock_accm_account_di/ads_stock_accm_account_di.tcl log=/data01/sqlldr/stg_data/ads_stock_accm_account_di/ads_stock_accm_account_di.log 


## 测试数据
hive -e "SELECT * FROM dm_acctanal.ads_stock_accm_account_di where busi_date = ********"  |  grep -v "WARN" > /data01/sqlldr/stg_data/ads_stock_accm_account_di/ads_stock_accm_account_di_test.dat

------------------------------------------------------------------------------------------------------------------------------------------------------


4.ads_stock_accm_profit_month_di

## hive表数据导出
hive -e "select  *
from dm_acctanal.ads_stock_accm_profit_month_di
where busi_date in ( '********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','********','20210331','20200331','20230430','20181031','20210731','20210228','20181231','20230928','20210831','20200630','20220630','20201130','20191031','20201031','20190531','20170930','20220831','20220731','20200131')"  |  grep -v "WARN" > /data01/sqlldr/stg_data/ads_stock_accm_profit_month_di/ads_stock_accm_profit_month_di.dat


/usr/lib/oracle/21/client64/bin/sqlldr SJZX/'ctzqxy262700!'@10.30.98.133:1521/dsjfxdb control=/data01/sqlldr/stg_data/ads_stock_accm_profit_month_di/ads_stock_accm_profit_month_di.tcl log=/data01/sqlldr/stg_data/ads_stock_accm_profit_month_di/ads_stock_accm_profit_month_di.log 


## 测试数据
hive -e "SELECT * FROM dm_acctanal.ads_stock_accm_profit_month_di where busi_date = ********"  |  grep -v "WARN" > /data01/sqlldr/stg_data/ads_stock_accm_profit_month_di/ads_stock_accm_profit_month_di_test.dat

------------------------------------------------------------------------------------------------------------------------------------------------------

5.dc.dwd_prd_stkprice_dd
## hive表数据导出
hive -e "SELECT * FROM dc.dwd_prd_stkprice_dd"  |  grep -v "WARN" > /data01/sqlldr/stg_data/dwd_prd_stkprice_dd/dwd_prd_stkprice_dd.dat


/usr/lib/oracle/21/client64/bin/sqlldr SJZX/'ctzqxy262700!'@10.30.98.133:1521/dsjfxdb control=/data01/sqlldr/stg_data/dwd_prd_stkprice_dd/dwd_prd_stkprice_dd.tcl log=/data01/sqlldr/stg_data/dwd_prd_stkprice_dd/dwd_prd_stkprice_dd.log 


## 测试数据
hive -e "SELECT * FROM dc.dwd_prd_stkprice_dd where busi_date = ********"  |  grep -v "WARN" > /data01/sqlldr/stg_data/dwd_prd_stkprice_dd/dwd_prd_stkprice_dd_test.dat 

------------------------------------------------------------------------------------------------------------------------------------------------------

6.ads_account_profit_accm_td_di
## hive表数据导出
hive -e "SELECT * FROM dm_acctanal.ads_account_profit_accm_td_di where busi_date >= ********"  |  grep -v "WARN" > /data01/sqlldr/stg_data/ads_account_profit_accm_td_di/ads_account_profit_accm_td_di.dat


/usr/lib/oracle/21/client64/bin/sqlldr SJZX/'ctzqxy262700!'@10.30.98.133:1521/dsjfxdb control=/data01/sqlldr/stg_data/ads_account_profit_accm_td_di/ads_account_profit_accm_td_di.tcl log=/data01/sqlldr/stg_data/ads_account_profit_accm_td_di/ads_account_profit_accm_td_di.log 


## 测试数据
hive -e "SELECT * FROM dm_acctanal.ads_account_profit_accm_td_di where busi_date = ********"  |  grep -v "WARN" > /data01/sqlldr/stg_data/ads_account_profit_accm_td_di/ads_account_profit_accm_td_di_test.dat

------------------------------------------------------------------------------------------------------------------------------------------------------

7.ads_stock_account_max_1y_di
## hive表数据导出
hive -e "SELECT * FROM dm_acctanal.ads_stock_account_max_1y_di where busi_date >= ********"  |  grep -v "WARN" > /data01/sqlldr/stg_data/ads_stock_account_max_1y_di/ads_stock_account_max_1y_di.dat


/usr/lib/oracle/21/client64/bin/sqlldr SJZX/'ctzqxy262700!'@10.30.98.133:1521/dsjfxdb control=/data01/sqlldr/stg_data/ads_stock_account_max_1y_di/ads_stock_account_max_1y_di.tcl log=/data01/sqlldr/stg_data/ads_stock_account_max_1y_di/ads_stock_account_max_1y_di.log 


## 测试数据
hive -e "SELECT * FROM dm_acctanal.ads_stock_account_max_1y_di where busi_date = ********"  |  grep -v "WARN" > /data01/sqlldr/stg_data/ads_stock_account_max_1y_di/ads_stock_account_max_1y_di.dat

------------------------------------------------------------------------------------------------------------------------------------------------------

7.his_deliver
## hive表数据导出
hive -e "SELECT init_date,serial_no,curr_time,business_flag,op_branch_no,op_entrust_way,exchange_type,stock_account,stock_code,stock_type,stock_name,store_unit,money_type,entrust_bs,seat_no,report_no,business_no,business_type,business_amount,business_price,business_balance,post_amount,report_time,business_time,business_times,clear_balance,post_balance,stock_interest,profit,fare0,fare1,fare2,fare3,farex,standard_fare0,exchange_fare,fare_remark,remark,entrust_date,entrust_no,square_flag,bank_no,branch_no,fund_account,client_id,report_account,date_back,real_status,stock_interestx,exchange_fare0,exchange_fare1,exchange_fare2,exchange_fare3,exchange_fare4,exchange_fare5,exchange_fare6,exchange_farex,rebate_balance,brokerage,position_str,client_group,room_code,asset_prop,risk_level,corp_client_group,corp_risk_level,asset_level,broker_account,client_name,holder_name,limit_flag,business_id,order_id,frozen_amount,unfrozen_amount,correct_amount,frozen_balance,unfrozen_balance,correct_balance,sub_stock_type,secu_stock_type,stock_code_long FROM uf20.his_deliver where part_column >= ********"  |  grep -v "WARN" > /data01/sqlldr/stg_data/his_deliver/his_deliver.dat


/usr/lib/oracle/21/client64/bin/sqlldr SJZX/'ctzqxy262700!'@10.30.98.133:1521/dsjfxdb control=/data01/sqlldr/stg_data/his_deliver/his_deliver.tcl log=/data01/sqlldr/stg_data/his_deliver/his_deliver.log 


## 测试数据
hive -e "SELECT * FROM dm_acctanal.ads_stock_account_max_1y_di where busi_date = ********"  |  grep -v "WARN" > /data01/sqlldr/stg_data/ads_stock_account_max_1y_di/ads_stock_account_max_1y_di.dat


## hive表数据导出
hive -e "SELECT * FROM dc.ads_par_client_level_base_dd"  |  grep -v "WARN" > /data01/sqlldr/stg_data/ads_par_client_level_base_dd/ads_par_client_level_base_dd.dat




/usr/lib/oracle/21/client64/bin/sqlldr CT_ODS_DC/CT_ODS_DC@10.30.98.81:1521/dcodsdb control=/data01/sqlldr/stg_data/ads_par_client_level_base_dd/ads_par_client_level_base_dd.tcl log=/data01/sqlldr/stg_data/ads_par_client_level_base_dd/ads_par_client_level_base_dd.log


## hive表数据导出
hive -e "SELECT INIT_DATE,CLIENT_ID,FUND_ACCOUNT,ACCOUNT_PROFIT,PROFIT_RATE,PROD_PROFIT,PROD_FARE,PROD_DIVIDEND_TAX,STOCK_PROFIT,STOCK_FARE,STOCK_FARE0,STOCK_FARE1,STOCK_FARE2,STOCK_FARE3,STOCK_FAREX,STOCK_DIVIDEND_TAX,UPDATE_TIME,BUSI_DATE,ACCOUNT_PROFIT_M,ACCOUNT_PROFIT_RATE_M,ACCOUNT_PROFIT_1Y,ACCOUNT_PROFIT_RATE_1Y,STOCK_PROFIT_M,STOCK_PROFIT_RATE_M,STOCK_PROFIT_1Y,STOCK_PROFIT_RATE_1Y,ACCOUNT_PROFIT_RATE_TD,STOCK_PROFIT_TD,STOCK_PROFIT_RATE_TD,STOCK_PROFIT_RATE,IS_TRADE_DAY_BOOL,ACCOUNT_PROFIT_TD,PROD_VALUE,STOCK_VALUE FROM dm_acctanal.ads_norm_acc_profit_day_df where busi_date <******** and busi_date >= ********"  |  grep -v "WARN" > /data01/sqlldr/stg_data/ads_norm_acc_profit_day_df/ads_norm_acc_profit_day_df_2023.dat


/usr/lib/oracle/21/client64/bin/sqlldr SJZX/'ctzqxy262700!'@10.30.98.133:1521/dsjfxdb control=/data01/sqlldr/stg_data/ads_norm_acc_profit_day_df/ads_norm_acc_profit_day_df.tcl log=/data01/sqlldr/stg_data/ads_norm_acc_profit_day_df/ads_norm_acc_profit_day_df.log


hive -e "SELECT INIT_DATE,CLIENT_ID,FUND_ACCOUNT,ACCOUNT_PROFIT,PROFIT_RATE,PROD_PROFIT,PROD_FARE,PROD_DIVIDEND_TAX,STOCK_PROFIT,STOCK_FARE,STOCK_FARE0,STOCK_FARE1,STOCK_FARE2,STOCK_FARE3,STOCK_FAREX,STOCK_DIVIDEND_TAX,UPDATE_TIME,BUSI_DATE,ACCOUNT_PROFIT_M,ACCOUNT_PROFIT_RATE_M,ACCOUNT_PROFIT_1Y,ACCOUNT_PROFIT_RATE_1Y,STOCK_PROFIT_M,STOCK_PROFIT_RATE_M,STOCK_PROFIT_1Y,STOCK_PROFIT_RATE_1Y,ACCOUNT_PROFIT_RATE_TD,STOCK_PROFIT_TD,STOCK_PROFIT_RATE_TD,STOCK_PROFIT_RATE,IS_TRADE_DAY_BOOL,ACCOUNT_PROFIT_TD,PROD_VALUE,STOCK_VALUE FROM dm_acctanal.ads_norm_acc_profit_day_df where busi_date <******** and busi_date >= ********"  |  grep -v "WARN" > /data01/sqlldr/stg_data/ads_norm_acc_profit_day_df/ads_norm_acc_profit_day_df_2017.dat



#大宽表ads_cc_index_analy_di
hive -e "SELECT init_date,p_branch_no,p_branch_name,branch_no,branch_name,client_id,client_name,organ_flag_code,client_level_code,corp_risk_level_code,client_status_code,organ_flag,client_level,corp_risk_level,client_status,open_date,party_no,party_name,party_type,relation_type,relation_start_date,sex,age,birthday,efficient_client_bool,high_worth_client_bool,drai_client_bool,drai_channel_code,inner_change_cust_bool,total_asset,net_asset,comm_total_value,crdt_total_value,opt_total_value,crdt_fin_slo_debit,crdt_fin_slo_balance,fund_asset,secu_mtk_value,opt_mtk_value,prod_mtk_value,equ_prodfund_mtk_value,fixr_prodfund_mtk_value,money_prodfund_mtk_value,pub_prodfund_mtk_value,pri_prodfund_mtk_value,total_trade_fare,total_trade_net_fare,comm_trade_fare,crdt_trade_fare,opt_trade_fare,assure_trade_fare,credit_trade_fare,comm_trade_net_fare,crdt_trade_net_fare,opt_trade_net_fare,assure_trade_net_fare,credit_trade_net_fare,comm_stockfund_net_fare,assure_stockfund_net_fare,credit_stockfund_net_fare,stockfund_trd_balance,comm_stockfund_trd_balance,crdt_stockfund_trd_balance,credit_stockfund_trd_balance,assure_stockfund_trd_balance,prodfund_buy_balance,equ_prodfund_buy_balance,fixr_prodfund_buy_balance,money_prodfund_buy_balance,pub_prodfund_buy_balance,pri_prodfund_buy_balance,avg_cust_total_asset_y,avg_total_asset_y,avg_cust_fin_slo_balance_y,avg_fin_slo_balance_y,avg_cust_net_asset_y,avg_net_asset_y,avg_cust_limitsecu_mtk_value_y,avg_limitsecu_mtk_value_y,avg_cust_comm_total_value_y,avg_comm_total_value_y,avg_cust_crdt_total_value_y,avg_crdt_total_value_y,avg_cust_opt_total_value_y,avg_opt_total_value_y,avg_cust_crdt_fin_slo_debit_y,avg_crdt_fin_slo_debit_y,avg_cust_prod_mtk_value_y,avg_prod_mtk_value_y,avg_equ_prodfund_mtk_value_y,avg_fixr_prodfund_mtk_value_y,avg_money_prodfund_mtk_value_y,avg_pub_prodfund_mtk_value_y,avg_pri_prodfund_mtk_value_y,total_trade_fare_y,total_trade_net_fare_y,comm_trade_fare_y,crdt_trade_fare_y,opt_trade_fare_y,assure_trade_fare_y,credit_trade_fare_y,comm_trade_net_fare_y,crdt_trade_net_fare_y,opt_trade_net_fare_y,assure_trade_net_fare_y,credit_trade_net_fare_y,comm_stockfund_net_fare_y,assure_stockfund_net_fare_y,credit_stockfund_net_fare_y,stockfund_trd_balance_y,comm_stockfund_trd_balance_y,crdt_stockfund_trd_balance_y,credit_stockfund_trd_balance_y,assure_stockfund_trd_balance_y,prodfund_buy_balance_y,equ_prodfund_buy_balance_y,fixr_prodfund_buy_balance_y,money_prodfund_buy_balance_y,pub_prodfund_buy_balance_y,pri_prodfund_buy_balance_y,avg_total_off_ct_asset_20d,avg_total_asset_20d,crdt_open_date,prod_efficient_client_bool,opt_fund_asset,comm_mtk_value,crdt_mtk_value,comb_mkt_value,avg_comb_mkt_value_y,exchfund_buy_balance_y,equ_exchfund_buy_balance_y,col13,col14,col15,col16,col17,col18,col19,update_time,busi_date,limit_secu_mtk_value,'',avg_cust_equ_prodfund_mtk_value_y FROM dc.ads_cc_index_analy_di WHERE busi_date >= 20220101 AND busi_date < ********" | grep -v "WARN" > /data01/sqlldr/stg_data/ads_cc_index_analy_di/ads_cc_index_analy_di_22.dat


/usr/lib/oracle/21/client64/bin/sqlldr PERFORMANCE/'performance@ct123!'@10.30.98.101:1521/ctjxzxdb control=/data01/sqlldr/stg_data/ads_cc_index_analy_di/ads_cc_index_analy_di.tcl log=/data01/sqlldr/stg_data/ads_cc_index_analy_di/ads_cc_index_analy_di.log

"PERFORMANCE/\"performance@ct123!\"@10.30.98.101:1521/ctjxzxdb"

