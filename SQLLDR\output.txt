INIT_DATE "case when UPPER(:INIT_DATE) = 'NULL' THEN '' ELSE :INIT_DATE END",
P_BRANCH_NO "case when UPPER(:P_<PERSON><PERSON>CH_NO) = 'NULL' THEN '' ELSE :P_BRANCH_NO END",
P_BRANCH_NAME "case when UPPER(:P_<PERSON>ANCH_NAME) = 'NULL' THEN '' ELSE :P_BRANCH_NAME END",
BRANCH_NO "case when UPPER(:BRANCH_NO) = 'NULL' THEN '' ELSE :BRANCH_NO END",
BRANCH_NAME "case when UPPER(:<PERSON><PERSON>CH_NAME) = 'NULL' THEN '' ELSE :BRANCH_NAME END",
CLIENT_ID "case when UPPER(:CLIENT_ID) = 'NULL' THEN '' ELSE :CLIENT_ID END",
CLIENT_NAME "case when UPPER(:CLIENT_NAME) = 'NULL' THEN '' ELSE :CLIENT_NAME END",
ORGAN_FLAG_CODE "case when UPPER(:OR<PERSON>N_FLAG_CODE) = 'NULL' THEN '' ELSE :ORGAN_FLAG_CODE END",
CLIENT_LEVEL_CODE "case when UPPER(:CLIENT_LEVEL_CODE) = 'NULL' THEN '' ELSE :CLIENT_LEVEL_CODE END",
CORP_RISK_LEVEL_CODE "case when UPPER(:CORP_RISK_LEVEL_CODE) = 'NULL' THEN '' ELSE :CORP_RISK_LEVEL_CODE END",
CLIENT_STATUS_CODE "case when UPPER(:CLIENT_STATUS_CODE) = 'NULL' THEN '' ELSE :CLIENT_STATUS_CODE END",
ORGAN_FLAG "case when UPPER(:ORGAN_FLAG) = 'NULL' THEN '' ELSE :ORGAN_FLAG END",
CLIENT_LEVEL "case when UPPER(:CLIENT_LEVEL) = 'NULL' THEN '' ELSE :CLIENT_LEVEL END",
CORP_RISK_LEVEL "case when UPPER(:CORP_RISK_LEVEL) = 'NULL' THEN '' ELSE :CORP_RISK_LEVEL END",
CLIENT_STATUS "case when UPPER(:CLIENT_STATUS) = 'NULL' THEN '' ELSE :CLIENT_STATUS END",
OPEN_DATE "case when UPPER(:OPEN_DATE) = 'NULL' THEN '' ELSE :OPEN_DATE END",
PARTY_NO "case when UPPER(:PARTY_NO) = 'NULL' THEN '' ELSE :PARTY_NO END",
PARTY_NAME "case when UPPER(:PARTY_NAME) = 'NULL' THEN '' ELSE :PARTY_NAME END",
PARTY_TYPE "case when UPPER(:PARTY_TYPE) = 'NULL' THEN '' ELSE :PARTY_TYPE END",
RELATION_TYPE "case when UPPER(:RELATION_TYPE) = 'NULL' THEN '' ELSE :RELATION_TYPE END",
RELATION_START_DATE "case when UPPER(:RELATION_START_DATE) = 'NULL' THEN '' ELSE :RELATION_START_DATE END",
SEX "case when UPPER(:SEX) = 'NULL' THEN '' ELSE :SEX END",
AGE "case when UPPER(:AGE) = 'NULL' THEN '' ELSE :AGE END",
BIRTHDAY "case when UPPER(:BIRTHDAY) = 'NULL' THEN '' ELSE :BIRTHDAY END",
EFFICIENT_CLIENT_BOOL "case when UPPER(:EFFICIENT_CLIENT_BOOL) = 'NULL' THEN '' ELSE :EFFICIENT_CLIENT_BOOL END",
HIGH_WORTH_CLIENT_BOOL "case when UPPER(:HIGH_WORTH_CLIENT_BOOL) = 'NULL' THEN '' ELSE :HIGH_WORTH_CLIENT_BOOL END",
DRAI_CLIENT_BOOL "case when UPPER(:DRAI_CLIENT_BOOL) = 'NULL' THEN '' ELSE :DRAI_CLIENT_BOOL END",
DRAI_CHANNEL_CODE "case when UPPER(:DRAI_CHANNEL_CODE) = 'NULL' THEN '' ELSE :DRAI_CHANNEL_CODE END",
INNER_CHANGE_CUST_BOOL "case when UPPER(:INNER_CHANGE_CUST_BOOL) = 'NULL' THEN '' ELSE :INNER_CHANGE_CUST_BOOL END",
TOTAL_ASSET "case when UPPER(:TOTAL_ASSET) = 'NULL' THEN '' ELSE :TOTAL_ASSET END",
NET_ASSET "case when UPPER(:NET_ASSET) = 'NULL' THEN '' ELSE :NET_ASSET END",
COMM_TOTAL_VALUE "case when UPPER(:COMM_TOTAL_VALUE) = 'NULL' THEN '' ELSE :COMM_TOTAL_VALUE END",
CRDT_TOTAL_VALUE "case when UPPER(:CRDT_TOTAL_VALUE) = 'NULL' THEN '' ELSE :CRDT_TOTAL_VALUE END",
OPT_TOTAL_VALUE "case when UPPER(:OPT_TOTAL_VALUE) = 'NULL' THEN '' ELSE :OPT_TOTAL_VALUE END",
CRDT_FIN_SLO_DEBIT "case when UPPER(:CRDT_FIN_SLO_DEBIT) = 'NULL' THEN '' ELSE :CRDT_FIN_SLO_DEBIT END",
CRDT_FIN_SLO_BALANCE "case when UPPER(:CRDT_FIN_SLO_BALANCE) = 'NULL' THEN '' ELSE :CRDT_FIN_SLO_BALANCE END",
FUND_ASSET "case when UPPER(:FUND_ASSET) = 'NULL' THEN '' ELSE :FUND_ASSET END",
SECU_MTK_VALUE "case when UPPER(:SECU_MTK_VALUE) = 'NULL' THEN '' ELSE :SECU_MTK_VALUE END",
OPT_MTK_VALUE "case when UPPER(:OPT_MTK_VALUE) = 'NULL' THEN '' ELSE :OPT_MTK_VALUE END",
PROD_MTK_VALUE "case when UPPER(:PROD_MTK_VALUE) = 'NULL' THEN '' ELSE :PROD_MTK_VALUE END",
EQU_PRODFUND_MTK_VALUE "case when UPPER(:EQU_PRODFUND_MTK_VALUE) = 'NULL' THEN '' ELSE :EQU_PRODFUND_MTK_VALUE END",
FIXR_PRODFUND_MTK_VALUE "case when UPPER(:FIXR_PRODFUND_MTK_VALUE) = 'NULL' THEN '' ELSE :FIXR_PRODFUND_MTK_VALUE END",
MONEY_PRODFUND_MTK_VALUE "case when UPPER(:MONEY_PRODFUND_MTK_VALUE) = 'NULL' THEN '' ELSE :MONEY_PRODFUND_MTK_VALUE END",
PUB_PRODFUND_MTK_VALUE "case when UPPER(:PUB_PRODFUND_MTK_VALUE) = 'NULL' THEN '' ELSE :PUB_PRODFUND_MTK_VALUE END",
PRI_PRODFUND_MTK_VALUE "case when UPPER(:PRI_PRODFUND_MTK_VALUE) = 'NULL' THEN '' ELSE :PRI_PRODFUND_MTK_VALUE END",
TOTAL_TRADE_FARE "case when UPPER(:TOTAL_TRADE_FARE) = 'NULL' THEN '' ELSE :TOTAL_TRADE_FARE END",
TOTAL_TRADE_NET_FARE "case when UPPER(:TOTAL_TRADE_NET_FARE) = 'NULL' THEN '' ELSE :TOTAL_TRADE_NET_FARE END",
COMM_TRADE_FARE "case when UPPER(:COMM_TRADE_FARE) = 'NULL' THEN '' ELSE :COMM_TRADE_FARE END",
CRDT_TRADE_FARE "case when UPPER(:CRDT_TRADE_FARE) = 'NULL' THEN '' ELSE :CRDT_TRADE_FARE END",
OPT_TRADE_FARE "case when UPPER(:OPT_TRADE_FARE) = 'NULL' THEN '' ELSE :OPT_TRADE_FARE END",
ASSURE_TRADE_FARE "case when UPPER(:ASSURE_TRADE_FARE) = 'NULL' THEN '' ELSE :ASSURE_TRADE_FARE END",
CREDIT_TRADE_FARE "case when UPPER(:CREDIT_TRADE_FARE) = 'NULL' THEN '' ELSE :CREDIT_TRADE_FARE END",
COMM_TRADE_NET_FARE "case when UPPER(:COMM_TRADE_NET_FARE) = 'NULL' THEN '' ELSE :COMM_TRADE_NET_FARE END",
CRDT_TRADE_NET_FARE "case when UPPER(:CRDT_TRADE_NET_FARE) = 'NULL' THEN '' ELSE :CRDT_TRADE_NET_FARE END",
OPT_TRADE_NET_FARE "case when UPPER(:OPT_TRADE_NET_FARE) = 'NULL' THEN '' ELSE :OPT_TRADE_NET_FARE END",
ASSURE_TRADE_NET_FARE "case when UPPER(:ASSURE_TRADE_NET_FARE) = 'NULL' THEN '' ELSE :ASSURE_TRADE_NET_FARE END",
CREDIT_TRADE_NET_FARE "case when UPPER(:CREDIT_TRADE_NET_FARE) = 'NULL' THEN '' ELSE :CREDIT_TRADE_NET_FARE END",
COMM_STOCKFUND_NET_FARE "case when UPPER(:COMM_STOCKFUND_NET_FARE) = 'NULL' THEN '' ELSE :COMM_STOCKFUND_NET_FARE END",
ASSURE_STOCKFUND_NET_FARE "case when UPPER(:ASSURE_STOCKFUND_NET_FARE) = 'NULL' THEN '' ELSE :ASSURE_STOCKFUND_NET_FARE END",
CREDIT_STOCKFUND_NET_FARE "case when UPPER(:CREDIT_STOCKFUND_NET_FARE) = 'NULL' THEN '' ELSE :CREDIT_STOCKFUND_NET_FARE END",
STOCKFUND_TRD_BALANCE "case when UPPER(:STOCKFUND_TRD_BALANCE) = 'NULL' THEN '' ELSE :STOCKFUND_TRD_BALANCE END",
COMM_STOCKFUND_TRD_BALANCE "case when UPPER(:COMM_STOCKFUND_TRD_BALANCE) = 'NULL' THEN '' ELSE :COMM_STOCKFUND_TRD_BALANCE END",
CRDT_STOCKFUND_TRD_BALANCE "case when UPPER(:CRDT_STOCKFUND_TRD_BALANCE) = 'NULL' THEN '' ELSE :CRDT_STOCKFUND_TRD_BALANCE END",
CREDIT_STOCKFUND_TRD_BALANCE "case when UPPER(:CREDIT_STOCKFUND_TRD_BALANCE) = 'NULL' THEN '' ELSE :CREDIT_STOCKFUND_TRD_BALANCE END",
ASSURE_STOCKFUND_TRD_BALANCE "case when UPPER(:ASSURE_STOCKFUND_TRD_BALANCE) = 'NULL' THEN '' ELSE :ASSURE_STOCKFUND_TRD_BALANCE END",
PRODFUND_BUY_BALANCE "case when UPPER(:PRODFUND_BUY_BALANCE) = 'NULL' THEN '' ELSE :PRODFUND_BUY_BALANCE END",
EQU_PRODFUND_BUY_BALANCE "case when UPPER(:EQU_PRODFUND_BUY_BALANCE) = 'NULL' THEN '' ELSE :EQU_PRODFUND_BUY_BALANCE END",
FIXR_PRODFUND_BUY_BALANCE "case when UPPER(:FIXR_PRODFUND_BUY_BALANCE) = 'NULL' THEN '' ELSE :FIXR_PRODFUND_BUY_BALANCE END",
MONEY_PRODFUND_BUY_BALANCE "case when UPPER(:MONEY_PRODFUND_BUY_BALANCE) = 'NULL' THEN '' ELSE :MONEY_PRODFUND_BUY_BALANCE END",
PUB_PRODFUND_BUY_BALANCE "case when UPPER(:PUB_PRODFUND_BUY_BALANCE) = 'NULL' THEN '' ELSE :PUB_PRODFUND_BUY_BALANCE END",
PRI_PRODFUND_BUY_BALANCE "case when UPPER(:PRI_PRODFUND_BUY_BALANCE) = 'NULL' THEN '' ELSE :PRI_PRODFUND_BUY_BALANCE END",
AVG_CUST_TOTAL_ASSET_Y "case when UPPER(:AVG_CUST_TOTAL_ASSET_Y) = 'NULL' THEN '' ELSE :AVG_CUST_TOTAL_ASSET_Y END",
AVG_TOTAL_ASSET_Y "case when UPPER(:AVG_TOTAL_ASSET_Y) = 'NULL' THEN '' ELSE :AVG_TOTAL_ASSET_Y END",
AVG_CUST_FIN_SLO_BALANCE_Y "case when UPPER(:AVG_CUST_FIN_SLO_BALANCE_Y) = 'NULL' THEN '' ELSE :AVG_CUST_FIN_SLO_BALANCE_Y END",
AVG_FIN_SLO_BALANCE_Y "case when UPPER(:AVG_FIN_SLO_BALANCE_Y) = 'NULL' THEN '' ELSE :AVG_FIN_SLO_BALANCE_Y END",
AVG_CUST_NET_ASSET_Y "case when UPPER(:AVG_CUST_NET_ASSET_Y) = 'NULL' THEN '' ELSE :AVG_CUST_NET_ASSET_Y END",
AVG_NET_ASSET_Y "case when UPPER(:AVG_NET_ASSET_Y) = 'NULL' THEN '' ELSE :AVG_NET_ASSET_Y END",
AVG_CUST_LIMITSECU_MTK_VALUE_Y "case when UPPER(:AVG_CUST_LIMITSECU_MTK_VALUE_Y) = 'NULL' THEN '' ELSE :AVG_CUST_LIMITSECU_MTK_VALUE_Y END",
AVG_LIMITSECU_MTK_VALUE_Y "case when UPPER(:AVG_LIMITSECU_MTK_VALUE_Y) = 'NULL' THEN '' ELSE :AVG_LIMITSECU_MTK_VALUE_Y END",
AVG_CUST_COMM_TOTAL_VALUE_Y "case when UPPER(:AVG_CUST_COMM_TOTAL_VALUE_Y) = 'NULL' THEN '' ELSE :AVG_CUST_COMM_TOTAL_VALUE_Y END",
AVG_COMM_TOTAL_VALUE_Y "case when UPPER(:AVG_COMM_TOTAL_VALUE_Y) = 'NULL' THEN '' ELSE :AVG_COMM_TOTAL_VALUE_Y END",
AVG_CUST_CRDT_TOTAL_VALUE_Y "case when UPPER(:AVG_CUST_CRDT_TOTAL_VALUE_Y) = 'NULL' THEN '' ELSE :AVG_CUST_CRDT_TOTAL_VALUE_Y END",
AVG_CRDT_TOTAL_VALUE_Y "case when UPPER(:AVG_CRDT_TOTAL_VALUE_Y) = 'NULL' THEN '' ELSE :AVG_CRDT_TOTAL_VALUE_Y END",
AVG_CUST_OPT_TOTAL_VALUE_Y "case when UPPER(:AVG_CUST_OPT_TOTAL_VALUE_Y) = 'NULL' THEN '' ELSE :AVG_CUST_OPT_TOTAL_VALUE_Y END",
AVG_OPT_TOTAL_VALUE_Y "case when UPPER(:AVG_OPT_TOTAL_VALUE_Y) = 'NULL' THEN '' ELSE :AVG_OPT_TOTAL_VALUE_Y END",
AVG_CUST_CRDT_FIN_SLO_DEBIT_Y "case when UPPER(:AVG_CUST_CRDT_FIN_SLO_DEBIT_Y) = 'NULL' THEN '' ELSE :AVG_CUST_CRDT_FIN_SLO_DEBIT_Y END",
AVG_CRDT_FIN_SLO_DEBIT_Y "case when UPPER(:AVG_CRDT_FIN_SLO_DEBIT_Y) = 'NULL' THEN '' ELSE :AVG_CRDT_FIN_SLO_DEBIT_Y END",
AVG_CUST_PROD_MTK_VALUE_Y "case when UPPER(:AVG_CUST_PROD_MTK_VALUE_Y) = 'NULL' THEN '' ELSE :AVG_CUST_PROD_MTK_VALUE_Y END",
AVG_PROD_MTK_VALUE_Y "case when UPPER(:AVG_PROD_MTK_VALUE_Y) = 'NULL' THEN '' ELSE :AVG_PROD_MTK_VALUE_Y END",
AVG_EQU_PRODFUND_MTK_VALUE_Y "case when UPPER(:AVG_EQU_PRODFUND_MTK_VALUE_Y) = 'NULL' THEN '' ELSE :AVG_EQU_PRODFUND_MTK_VALUE_Y END",
AVG_FIXR_PRODFUND_MTK_VALUE_Y "case when UPPER(:AVG_FIXR_PRODFUND_MTK_VALUE_Y) = 'NULL' THEN '' ELSE :AVG_FIXR_PRODFUND_MTK_VALUE_Y END",
AVG_MONEY_PRODFUND_MTK_VALUE_Y "case when UPPER(:AVG_MONEY_PRODFUND_MTK_VALUE_Y) = 'NULL' THEN '' ELSE :AVG_MONEY_PRODFUND_MTK_VALUE_Y END",
AVG_PUB_PRODFUND_MTK_VALUE_Y "case when UPPER(:AVG_PUB_PRODFUND_MTK_VALUE_Y) = 'NULL' THEN '' ELSE :AVG_PUB_PRODFUND_MTK_VALUE_Y END",
AVG_PRI_PRODFUND_MTK_VALUE_Y "case when UPPER(:AVG_PRI_PRODFUND_MTK_VALUE_Y) = 'NULL' THEN '' ELSE :AVG_PRI_PRODFUND_MTK_VALUE_Y END",
TOTAL_TRADE_FARE_Y "case when UPPER(:TOTAL_TRADE_FARE_Y) = 'NULL' THEN '' ELSE :TOTAL_TRADE_FARE_Y END",
TOTAL_TRADE_NET_FARE_Y "case when UPPER(:TOTAL_TRADE_NET_FARE_Y) = 'NULL' THEN '' ELSE :TOTAL_TRADE_NET_FARE_Y END",
COMM_TRADE_FARE_Y "case when UPPER(:COMM_TRADE_FARE_Y) = 'NULL' THEN '' ELSE :COMM_TRADE_FARE_Y END",
CRDT_TRADE_FARE_Y "case when UPPER(:CRDT_TRADE_FARE_Y) = 'NULL' THEN '' ELSE :CRDT_TRADE_FARE_Y END",
OPT_TRADE_FARE_Y "case when UPPER(:OPT_TRADE_FARE_Y) = 'NULL' THEN '' ELSE :OPT_TRADE_FARE_Y END",
ASSURE_TRADE_FARE_Y "case when UPPER(:ASSURE_TRADE_FARE_Y) = 'NULL' THEN '' ELSE :ASSURE_TRADE_FARE_Y END",
CREDIT_TRADE_FARE_Y "case when UPPER(:CREDIT_TRADE_FARE_Y) = 'NULL' THEN '' ELSE :CREDIT_TRADE_FARE_Y END",
COMM_TRADE_NET_FARE_Y "case when UPPER(:COMM_TRADE_NET_FARE_Y) = 'NULL' THEN '' ELSE :COMM_TRADE_NET_FARE_Y END",
CRDT_TRADE_NET_FARE_Y "case when UPPER(:CRDT_TRADE_NET_FARE_Y) = 'NULL' THEN '' ELSE :CRDT_TRADE_NET_FARE_Y END",
OPT_TRADE_NET_FARE_Y "case when UPPER(:OPT_TRADE_NET_FARE_Y) = 'NULL' THEN '' ELSE :OPT_TRADE_NET_FARE_Y END",
ASSURE_TRADE_NET_FARE_Y "case when UPPER(:ASSURE_TRADE_NET_FARE_Y) = 'NULL' THEN '' ELSE :ASSURE_TRADE_NET_FARE_Y END",
CREDIT_TRADE_NET_FARE_Y "case when UPPER(:CREDIT_TRADE_NET_FARE_Y) = 'NULL' THEN '' ELSE :CREDIT_TRADE_NET_FARE_Y END",
COMM_STOCKFUND_NET_FARE_Y "case when UPPER(:COMM_STOCKFUND_NET_FARE_Y) = 'NULL' THEN '' ELSE :COMM_STOCKFUND_NET_FARE_Y END",
ASSURE_STOCKFUND_NET_FARE_Y "case when UPPER(:ASSURE_STOCKFUND_NET_FARE_Y) = 'NULL' THEN '' ELSE :ASSURE_STOCKFUND_NET_FARE_Y END",
CREDIT_STOCKFUND_NET_FARE_Y "case when UPPER(:CREDIT_STOCKFUND_NET_FARE_Y) = 'NULL' THEN '' ELSE :CREDIT_STOCKFUND_NET_FARE_Y END",
STOCKFUND_TRD_BALANCE_Y "case when UPPER(:STOCKFUND_TRD_BALANCE_Y) = 'NULL' THEN '' ELSE :STOCKFUND_TRD_BALANCE_Y END",
COMM_STOCKFUND_TRD_BALANCE_Y "case when UPPER(:COMM_STOCKFUND_TRD_BALANCE_Y) = 'NULL' THEN '' ELSE :COMM_STOCKFUND_TRD_BALANCE_Y END",
CRDT_STOCKFUND_TRD_BALANCE_Y "case when UPPER(:CRDT_STOCKFUND_TRD_BALANCE_Y) = 'NULL' THEN '' ELSE :CRDT_STOCKFUND_TRD_BALANCE_Y END",
CREDIT_STOCKFUND_TRD_BALANCE_Y "case when UPPER(:CREDIT_STOCKFUND_TRD_BALANCE_Y) = 'NULL' THEN '' ELSE :CREDIT_STOCKFUND_TRD_BALANCE_Y END",
ASSURE_STOCKFUND_TRD_BALANCE_Y "case when UPPER(:ASSURE_STOCKFUND_TRD_BALANCE_Y) = 'NULL' THEN '' ELSE :ASSURE_STOCKFUND_TRD_BALANCE_Y END",
PRODFUND_BUY_BALANCE_Y "case when UPPER(:PRODFUND_BUY_BALANCE_Y) = 'NULL' THEN '' ELSE :PRODFUND_BUY_BALANCE_Y END",
EQU_PRODFUND_BUY_BALANCE_Y "case when UPPER(:EQU_PRODFUND_BUY_BALANCE_Y) = 'NULL' THEN '' ELSE :EQU_PRODFUND_BUY_BALANCE_Y END",
FIXR_PRODFUND_BUY_BALANCE_Y "case when UPPER(:FIXR_PRODFUND_BUY_BALANCE_Y) = 'NULL' THEN '' ELSE :FIXR_PRODFUND_BUY_BALANCE_Y END",
MONEY_PRODFUND_BUY_BALANCE_Y "case when UPPER(:MONEY_PRODFUND_BUY_BALANCE_Y) = 'NULL' THEN '' ELSE :MONEY_PRODFUND_BUY_BALANCE_Y END",
PUB_PRODFUND_BUY_BALANCE_Y "case when UPPER(:PUB_PRODFUND_BUY_BALANCE_Y) = 'NULL' THEN '' ELSE :PUB_PRODFUND_BUY_BALANCE_Y END",
PRI_PRODFUND_BUY_BALANCE_Y "case when UPPER(:PRI_PRODFUND_BUY_BALANCE_Y) = 'NULL' THEN '' ELSE :PRI_PRODFUND_BUY_BALANCE_Y END",
AVG_TOTAL_OFF_CT_ASSET_20D "case when UPPER(:AVG_TOTAL_OFF_CT_ASSET_20D) = 'NULL' THEN '' ELSE :AVG_TOTAL_OFF_CT_ASSET_20D END",
AVG_TOTAL_ASSET_20D "case when UPPER(:AVG_TOTAL_ASSET_20D) = 'NULL' THEN '' ELSE :AVG_TOTAL_ASSET_20D END",
CRDT_OPEN_DATE "case when UPPER(:CRDT_OPEN_DATE) = 'NULL' THEN '' ELSE :CRDT_OPEN_DATE END",
PROD_EFFICIENT_CLIENT_BOOL "case when UPPER(:PROD_EFFICIENT_CLIENT_BOOL) = 'NULL' THEN '' ELSE :PROD_EFFICIENT_CLIENT_BOOL END",
OPT_FUND_ASSET "case when UPPER(:OPT_FUND_ASSET) = 'NULL' THEN '' ELSE :OPT_FUND_ASSET END",
COMM_MTK_VALUE "case when UPPER(:COMM_MTK_VALUE) = 'NULL' THEN '' ELSE :COMM_MTK_VALUE END",
CRDT_MTK_VALUE "case when UPPER(:CRDT_MTK_VALUE) = 'NULL' THEN '' ELSE :CRDT_MTK_VALUE END",
COMB_MKT_VALUE "case when UPPER(:COMB_MKT_VALUE) = 'NULL' THEN '' ELSE :COMB_MKT_VALUE END",
AVG_COMB_MKT_VALUE_Y "case when UPPER(:AVG_COMB_MKT_VALUE_Y) = 'NULL' THEN '' ELSE :AVG_COMB_MKT_VALUE_Y END",
EXCHFUND_BUY_BALANCE_Y "case when UPPER(:EXCHFUND_BUY_BALANCE_Y) = 'NULL' THEN '' ELSE :EXCHFUND_BUY_BALANCE_Y END",
EQU_EXCHFUND_BUY_BALANCE_Y "case when UPPER(:EQU_EXCHFUND_BUY_BALANCE_Y) = 'NULL' THEN '' ELSE :EQU_EXCHFUND_BUY_BALANCE_Y END",
COL13 "case when UPPER(:COL13) = 'NULL' THEN '' ELSE :COL13 END",
COL14 "case when UPPER(:COL14) = 'NULL' THEN '' ELSE :COL14 END",
COL15 "case when UPPER(:COL15) = 'NULL' THEN '' ELSE :COL15 END",
COL16 "case when UPPER(:COL16) = 'NULL' THEN '' ELSE :COL16 END",
COL17 "case when UPPER(:COL17) = 'NULL' THEN '' ELSE :COL17 END",
COL18 "case when UPPER(:COL18) = 'NULL' THEN '' ELSE :COL18 END",
COL19 "case when UPPER(:COL19) = 'NULL' THEN '' ELSE :COL19 END",
UPDATE_TIME "case when UPPER(:UPDATE_TIME) = 'NULL' THEN '' ELSE :UPDATE_TIME END",
BUSI_DATE "case when UPPER(:BUSI_DATE) = 'NULL' THEN '' ELSE :BUSI_DATE END",
LIMIT_SECU_MTK_VALUE "case when UPPER(:LIMIT_SECU_MTK_VALUE) = 'NULL' THEN '' ELSE :LIMIT_SECU_MTK_VALUE END",
UPDATETIME "case when UPPER(:UPDATETIME) = 'NULL' THEN '' ELSE :UPDATETIME END",
AVG_CUST_EQU_PRODFUND_MTK_VALU "case when UPPER(:AVG_CUST_EQU_PRODFUND_MTK_VALU) = 'NULL' THEN '' ELSE :AVG_CUST_EQU_PRODFUND_MTK_VALU END"
