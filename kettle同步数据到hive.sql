CREATE EXTERNAL TABLE `ods_jzbbb.nc_complex_value`(
  `index_date` double,
  `index_id` string,
  `caliber_no` double,
  `index_balance` double,
  `update_time` string,
  `quote_caliber_no` double,
  `report_id` string)
ROW FORMAT SERDE
  'org.apache.hadoop.hive.ql.io.parquet.serde.ParquetHiveSerDe'
STORED AS INPUTFORMAT
  'org.apache.hadoop.hive.ql.io.parquet.MapredParquetInputFormat'
OUTPUTFORMAT
  'org.apache.hadoop.hive.ql.io.parquet.MapredParquetOutputFormat'
LOCATION
  'hdfs://nameservice1/data/ods_jzbbb.db/ods_jzbbb/nc_complex_value'
TBLPROPERTIES (
  'COLUMN_STATS_ACCURATE'='false',
  'numFiles'='0',
  'numRows'='-1',
  'rawDataSize'='-1',
  'spark.sql.create.version'='2.2 or prior',
  'spark.sql.sources.schema.numParts'='1',
  'spark.sql.sources.schema.part.0'='{\"type\":\"struct\",\"fields\":[{\"name\":\"index_date\",\"type\":\"double\",\"nullable\":true,\"metadata\":{}},{\"name\":\"index_id\",\"type\":\"string\",\"nullable\":true,\"metadata\":{}},{\"name\":\"caliber_no\",\"type\":\"double\",\"nullable\":true,\"metadata\":{}},{\"name\":\"index_balance\",\"type\":\"double\",\"nullable\":true,\"metadata\":{}},{\"name\":\"update_time\",\"type\":\"string\",\"nullable\":true,\"metadata\":{}},{\"name\":\"quote_caliber_no\",\"type\":\"double\",\"nullable\":true,\"metadata\":{}},{\"name\":\"report_id\",\"type\":\"string\",\"nullable\":true,\"metadata\":{}}]}',
  'totalSize'='0',
  'transient_lastDdlTime'='1680486948')





CREATE TABLE `ods_jzbbb.stg_nc_complex_value`(
`index_date` double,
`index_id` string,
`caliber_no` double,
`index_balance` double,
`update_time` string,
`quote_caliber_no` double,
`report_id` string)
ROW FORMAT SERDE 
  'org.apache.hadoop.hive.serde2.lazy.LazySimpleSerDe' 
STORED AS INPUTFORMAT 
  'org.apache.hadoop.mapred.TextInputFormat' 
OUTPUTFORMAT 
  'org.apache.hadoop.hive.ql.io.HiveIgnoreKeyTextOutputFormat'
LOCATION
  'hdfs://nameservice1/data/ods_jzbbb.db/stg_nc_complex_value'
TBLPROPERTIES (
  'transient_lastDdlTime'='20250408');


insert overwrite TABLE ods_jzbbb.nc_complex_value select * from ods_jzbbb.stg_nc_complex_value;


insert_nc_complex_value
sh /opt/script/kettle/kettle_start.sh trans '/Trans/ods_jzbbb|stg_nc_complex_value'
sh /opt/script/kettle/kettle_start.sh trans '/Script/HIVESQL|insert_nc_complex_value'
