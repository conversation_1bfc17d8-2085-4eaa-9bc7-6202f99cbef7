#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
表名转换工具
功能:将空格分隔的表名转换为特定格式的字符串
作者:AI Assistant
"""

import sys
import tkinter as tk
from tkinter import scrolledtext, messagebox, ttk
from typing import Optional, List

# 默认配置
DEFAULT_CONFIG = {
    'prefix': '/^',
    'suffix': '$/',
    'separator': '$|^'
}

# 默认表名列表
DEFAULT_TABLES = """
HS_HIS	HIS_OPTCODE
HS_SECU	LIMITSELLSTOCK
HS_USER	UNDERLYCODE
HS_HIS	HIS_DATAOPTHOLD
HS_ASSET	SSTRESTRADEACCT
HS_PROD	PRODDELAYDATE
HS_SETT	INITCONFIG
HS_USER	SYSNODE
HS_USER	ROLES
HS_ASSET	CLIENTMAIL
HS_ASSET	ELIGPAPERQUESTREL
HS_PROD	PRODRATIONTIME
HS_ACPT	BOPBUSINTYPEQUALIARG
HS_ACPT	BOPCATEGORY
HS_ACPT	BOPQUALIARG
HS_ASSET	ASSETCLTLIMITSTOCK
HS_ASSET	ELIGPAPER
HS_HIS	HIS_OFDELIVER
"""

def validate_table_names(tables_str: str) -> None:
    """
    验证输入的表名格式是否正确
    
    Args:
        tables_str: 输入的表名字符串
        
    Raises:
        ValueError: 当输入格式不正确时抛出
    """
    if not tables_str or not isinstance(tables_str, str):
        raise ValueError("输入必须是非空字符串")
    
    for line in tables_str.splitlines():
        line = line.strip()
        if line:  # 跳过空行
            parts = line.split()
            if len(parts) != 2:
                raise ValueError(f"无效的表名格式: {line},每行应该包含两部分,用空格分隔")

def process_tables(
    tables_str: Optional[str] = None,
    prefix: str = DEFAULT_CONFIG['prefix'],
    suffix: str = DEFAULT_CONFIG['suffix'],
    separator: str = DEFAULT_CONFIG['separator']
) -> str:
    """
    处理表名列表,将空格分隔的表名转换为特定格式
    
    Args:
        tables_str: 输入的表名字符串,如果为None则使用默认值
        prefix: 结果字符串的前缀
        suffix: 结果字符串的后缀
        separator: 表名之间的分隔符
    
    Returns:
        处理后的表名字符串
        
    Raises:
        ValueError: 当输入格式不正确时抛出
    """
    try:
        # 使用默认值或传入值
        tables_str = tables_str if tables_str is not None else DEFAULT_TABLES
        
        # 验证输入
        validate_table_names(tables_str)
        
        # 处理表名:去除空行,替换空白字符为点号
        processed = [
            '.'.join(line.strip().split())
            for line in tables_str.splitlines()
            if line.strip()
        ]
        
        # 组合结果
        result = f"{prefix}{separator.join(processed)}{suffix}"
        return result
        
    except Exception as e:
        raise ValueError(f"处理失败: {str(e)}")

class TableConverterApp:
    """表名转换工具GUI应用"""
    
    def __init__(self, root):
        self.root = root
        self.root.title("表名转换工具")
        self.root.geometry("900x600")
        
        # 设置样式
        self.style = ttk.Style()
        self.style.configure("TButton", padding=6, relief="flat", font=("微软雅黑", 10))
        self.style.configure("TLabel", font=("微软雅黑", 10))
        self.style.configure("TFrame", background="#f0f0f0")
        
        self.create_widgets()
        
    def create_widgets(self):
        """创建GUI组件"""
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 顶部区域 - 配置选项
        top_config_frame = ttk.LabelFrame(main_frame, text="配置选项", padding="10")
        top_config_frame.pack(fill=tk.X, pady=(0, 5))
        
        # 配置参数在一行显示
        params_frame = ttk.Frame(top_config_frame)
        params_frame.pack(fill=tk.X, pady=2)
        
        # 前缀
        ttk.Label(params_frame, text="前缀:").pack(side=tk.LEFT)
        self.prefix_var = tk.StringVar(value=DEFAULT_CONFIG['prefix'])
        ttk.Entry(params_frame, textvariable=self.prefix_var, width=15).pack(side=tk.LEFT, padx=(5, 20))
        
        # 后缀
        ttk.Label(params_frame, text="后缀:").pack(side=tk.LEFT)
        self.suffix_var = tk.StringVar(value=DEFAULT_CONFIG['suffix'])
        ttk.Entry(params_frame, textvariable=self.suffix_var, width=15).pack(side=tk.LEFT, padx=(5, 20))
        
        # 分隔符
        ttk.Label(params_frame, text="分隔符:").pack(side=tk.LEFT)
        self.separator_var = tk.StringVar(value=DEFAULT_CONFIG['separator'])
        ttk.Entry(params_frame, textvariable=self.separator_var, width=15).pack(side=tk.LEFT, padx=(5, 20))
        
        # 顶部区域 - 按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Button(button_frame, text="转换", command=self.convert).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="复制结果", command=self.copy_result).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="清空", command=self.clear).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="重置为默认值", command=self.reset_to_default).pack(side=tk.LEFT, padx=5)
        
        # 内容区域 - 左右分栏
        content_frame = ttk.Frame(main_frame)
        content_frame.pack(fill=tk.BOTH, expand=True)
        content_frame.columnconfigure(0, weight=1)
        content_frame.columnconfigure(1, weight=1)
        
        # 左边输入区域
        input_frame = ttk.LabelFrame(content_frame, text="输入表名列表 (格式: 库名 表名)", padding="10")
        input_frame.grid(row=0, column=0, sticky="nsew", padx=(0, 5))
        
        self.input_text = scrolledtext.ScrolledText(input_frame, wrap=tk.WORD, font=("Consolas", 11))
        self.input_text.pack(fill=tk.BOTH, expand=True)
        self.input_text.insert(tk.END, DEFAULT_TABLES.strip())
        
        # 右边输出区域
        output_frame = ttk.LabelFrame(content_frame, text="转换结果", padding="10")
        output_frame.grid(row=0, column=1, sticky="nsew", padx=(5, 0))
        
        self.output_text = scrolledtext.ScrolledText(output_frame, wrap=tk.WORD, font=("Consolas", 11))
        self.output_text.pack(fill=tk.BOTH, expand=True)
        
    def convert(self):
        """执行转换操作"""
        try:
            tables_str = self.input_text.get("1.0", tk.END)
            prefix = self.prefix_var.get()
            suffix = self.suffix_var.get()
            separator = self.separator_var.get()
            
            result = process_tables(tables_str, prefix, suffix, separator)
            
            # 显示结果
            self.output_text.delete("1.0", tk.END)
            self.output_text.insert(tk.END, result)
            
        except Exception as e:
            messagebox.showerror("错误", str(e))
    
    def copy_result(self):
        """复制结果到剪贴板"""
        result = self.output_text.get("1.0", tk.END).strip()
        if result:
            self.root.clipboard_clear()
            self.root.clipboard_append(result)
            messagebox.showinfo("复制成功", "结果已复制到剪贴板")
        else:
            messagebox.showwarning("警告", "没有可复制的内容")
    
    def clear(self):
        """清空输入和输出"""
        self.input_text.delete("1.0", tk.END)
        self.output_text.delete("1.0", tk.END)
    
    def reset_to_default(self):
        """重置为默认值"""
        self.input_text.delete("1.0", tk.END)
        self.input_text.insert(tk.END, DEFAULT_TABLES.strip())
        
        self.prefix_var.set(DEFAULT_CONFIG['prefix'])
        self.suffix_var.set(DEFAULT_CONFIG['suffix'])
        self.separator_var.set(DEFAULT_CONFIG['separator'])
        
        self.output_text.delete("1.0", tk.END)

def main():
    """主函数"""
    try:
        root = tk.Tk()
        app = TableConverterApp(root)
        root.mainloop()
        return 0
    except Exception as e:
        messagebox.showerror("程序错误", f"程序执行失败: {str(e)}")
        return 1

if __name__ == '__main__':
    sys.exit(main())